import 'package:flutter/foundation.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/user_statistics.dart';
import '../models/user_model.dart';
import '../models/mind_map.dart';
import '../models/post.dart';

class UserStatisticsProvider extends ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  
  UserStatistics? _userStatistics;
  bool _isLoading = false;
  String? _error;
  
  // Getters
  UserStatistics? get userStatistics => _userStatistics;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // تحميل إحصائيات مستخدم محدد
  Future<void> loadUserStatistics(String userId) async {
    _setLoading(true);
    _clearError();
    
    try {
      debugPrint('🔄 بدء تحميل إحصائيات المستخدم: $userId');
      
      // تحميل بيانات المستخدم الأساسية
      final userSnapshot = await _database.ref('users/$userId').get();
      if (!userSnapshot.exists || userSnapshot.value == null) {
        throw Exception('المستخدم غير موجود');
      }

      final userData = _convertToStringDynamicMap(userSnapshot.value);
      final user = UserModel.fromMap(userData);
      
      // حساب إحصائيات المخططات الذهنية
      final mindMapStats = await _calculateMindMapStatistics(userId);
      
      // حساب إحصائيات المنشورات
      final postStats = await _calculatePostStatistics(userId);
      
      // حساب إحصائيات التفاعل
      final interactionStats = await _calculateInteractionStatistics(userId);
      
      // حساب إحصائيات الوقت والنشاط
      final timeStats = await _calculateTimeStatistics(userId, user);
      
      // إنشاء كائن الإحصائيات
      _userStatistics = UserStatistics(
        userId: userId,
        userName: user.fullName,
        userEmail: user.email,
        totalMindMaps: mindMapStats['total'],
        favoriteMindMaps: mindMapStats['favorites'],
        publishedMindMaps: mindMapStats['published'],
        templatedMindMaps: mindMapStats['templated'],
        mindMapsBySubject: mindMapStats['bySubject'],
        totalPosts: postStats['total'],
        totalLikes: postStats['likes'],
        totalReactions: postStats['reactions'],
        totalComments: postStats['comments'],
        totalCommentsGiven: interactionStats['commentsGiven'],
        totalReactionsGiven: interactionStats['reactionsGiven'],
        totalFollowers: user.followersCount,
        totalFollowing: user.followingCount,
        totalTimeSpent: timeStats['totalTime'],
        firstLoginDate: user.createdAt,
        lastActiveDate: user.lastActiveAt,
        totalSessions: timeStats['sessions'],
        averageSessionDuration: timeStats['avgSession'],
        activeDaysCount: timeStats['activeDays'],
        activityByDay: timeStats['byDay'],
        activityByMonth: timeStats['byMonth'],
      );
      
      debugPrint('✅ تم تحميل إحصائيات المستخدم بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إحصائيات المستخدم: $e');
      _setError('خطأ في تحميل الإحصائيات: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // حساب إحصائيات المخططات الذهنية
  Future<Map<String, dynamic>> _calculateMindMapStatistics(String userId) async {
    try {
      debugPrint('🔍 بدء حساب إحصائيات المخططات للمستخدم: $userId');
      final mindMapsRef = _database.ref('userProjects/$userId/mindMaps');
      final snapshot = await mindMapsRef.get();

      debugPrint('📊 حالة المخططات: exists=${snapshot.exists}, value=${snapshot.value}');

      int total = 0;
      int favorites = 0;
      int published = 0;
      int templated = 0;
      Map<String, int> bySubject = {};

      if (snapshot.exists && snapshot.value != null) {
        try {
          final dynamic rawData = snapshot.value;
          debugPrint('🗺️ نوع البيانات الخام للمخططات: ${rawData.runtimeType}');

          if (rawData is Map) {
            final mindMapsData = _convertToStringDynamicMap(rawData);
            debugPrint('📋 عدد المخططات الموجودة: ${mindMapsData.length}');

            for (final entry in mindMapsData.entries) {
              // تحقق من أن القيمة ليست null
              if (entry.value == null) {
                debugPrint('⚠️ تخطي مخطط فارغ: ${entry.key}');
                continue;
              }
              try {
                debugPrint('🔄 معالجة مخطط: ${entry.key}, نوع البيانات: ${entry.value.runtimeType}');

                final mindMapData = _convertToStringDynamicMap(entry.value);
                debugPrint('✅ تم تحويل البيانات بنجاح');

                // التحقق من وجود الحقول المطلوبة
                if (mindMapData['id'] == null || mindMapData['title'] == null) {
                  debugPrint('⚠️ بيانات مخطط ناقصة: ${entry.key}');
                  continue;
                }

                // محاولة إنشاء MindMap مع معالجة أفضل للأخطاء
                MindMap? mindMap;
                try {
                  mindMap = MindMap.fromJson(mindMapData);
                } catch (mindMapError) {
                  debugPrint('❌ خطأ في إنشاء MindMap: $mindMapError');
                  // حساب الإحصائيات الأساسية من البيانات الخام
                  total++;
                  if (mindMapData['isFavorite'] == true) favorites++;
                  if (mindMapData['isPublished'] == true) published++;
                  if (mindMapData['isFromTemplate'] == true) templated++;

                  final subject = mindMapData['subject']?.toString() ?? '';
                  if (subject.isNotEmpty) {
                    bySubject[subject] = (bySubject[subject] ?? 0) + 1;
                  }
                  continue;
                }

                total++;
                debugPrint('✅ معالجة مخطط: ${mindMap.title}');

                if (mindMap.isFavorite) favorites++;
                if (mindMap.isPublished) published++;
                if (mindMap.isFromTemplate) templated++;

                // تجميع حسب المادة
                if (mindMap.subject.isNotEmpty) {
                  bySubject[mindMap.subject] = (bySubject[mindMap.subject] ?? 0) + 1;
                }
              } catch (e) {
                debugPrint('❌ خطأ في معالجة مخطط ذهني: $e');
                debugPrint('❌ مفتاح المخطط: ${entry.key}');
                debugPrint('❌ نوع البيانات: ${entry.value.runtimeType}');

                // محاولة حساب إحصائيات أساسية حتى لو فشل التحويل
                try {
                  if (entry.value is Map) {
                    final rawData = entry.value as Map;
                    total++;
                    if (rawData['isFavorite'] == true) favorites++;
                    if (rawData['isPublished'] == true) published++;
                    if (rawData['isFromTemplate'] == true) templated++;

                    final subject = rawData['subject']?.toString() ?? '';
                    if (subject.isNotEmpty) {
                      bySubject[subject] = (bySubject[subject] ?? 0) + 1;
                    }
                  }
                } catch (fallbackError) {
                  debugPrint('❌ فشل في الحساب الاحتياطي: $fallbackError');
                }
              }
            }
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحويل بيانات المخططات: $e');
        }
      } else {
        debugPrint('⚠️ لا توجد مخططات ذهنية للمستخدم');
      }

      debugPrint('📊 إحصائيات المخططات: total=$total, favorites=$favorites, published=$published, templated=$templated');

      return {
        'total': total,
        'favorites': favorites,
        'published': published,
        'templated': templated,
        'bySubject': bySubject,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات المخططات: $e');
      return {
        'total': 0,
        'favorites': 0,
        'published': 0,
        'templated': 0,
        'bySubject': <String, int>{},
      };
    }
  }
  
  // حساب إحصائيات المنشورات
  Future<Map<String, dynamic>> _calculatePostStatistics(String userId) async {
    try {
      debugPrint('🔍 بدء حساب إحصائيات المنشورات للمستخدم: $userId');
      final postsRef = _database.ref('posts');
      final snapshot = await postsRef.get();

      debugPrint('📊 حالة المنشورات: exists=${snapshot.exists}, value=${snapshot.value}');

      int total = 0;
      int likes = 0;
      int reactions = 0;
      int comments = 0;

      if (snapshot.exists && snapshot.value != null) {
        try {
          final dynamic rawData = snapshot.value;
          debugPrint('📝 نوع البيانات الخام للمنشورات: ${rawData.runtimeType}');

          if (rawData is Map) {
            final postsData = _convertToStringDynamicMap(rawData);
            debugPrint('📋 عدد المنشورات الموجودة: ${postsData.length}');

            for (final entry in postsData.entries) {
              try {
                // تحقق من أن القيمة ليست null
                if (entry.value == null) {
                  debugPrint('⚠️ تخطي منشور فارغ: ${entry.key}');
                  continue;
                }

                final postData = _convertToStringDynamicMap(entry.value);

                if (postData['authorId'] == userId) {
                  total++;
                  debugPrint('✅ معالجة منشور للمستخدم: ${postData['mindMapTitle']}');
                  debugPrint('📝 بيانات المنشور: likes=${postData['likes']}, reactions=${postData['reactions']}, comments=${postData['comments']}');

                  // حساب الإعجابات
                  final postLikes = postData['likes'];
                  if (postLikes != null) {
                    if (postLikes is List) {
                      likes += postLikes.length;
                    } else if (postLikes is Map) {
                      likes += postLikes.length;
                    }
                  }

                  // حساب ردود الفعل (البنية الجديدة)
                  final reactionsData = postData['reactions'];
                  if (reactionsData != null && reactionsData is Map) {
                    try {
                      // البنية الجديدة: reactions: { counts: {like: 5, love: 3}, userIds: {...} }
                      final countsData = reactionsData['counts'];
                      if (countsData != null && countsData is Map) {
                        for (final count in countsData.values) {
                          if (count is int) {
                            reactions += count;
                          }
                        }
                      } else {
                        // البنية القديمة: reactions: { like: { users: [...] }, love: { users: [...] } }
                        for (final reactionType in reactionsData.values) {
                          if (reactionType is Map) {
                            final usersList = reactionType['users'];
                            if (usersList != null) {
                              if (usersList is List) {
                                reactions += usersList.length;
                              } else if (usersList is Map) {
                                reactions += usersList.length;
                              }
                            }
                          }
                        }
                      }
                    } catch (e) {
                      debugPrint('❌ خطأ في معالجة ردود الفعل: $e');
                    }
                  }

                  // حساب التعليقات
                  final commentsData = postData['comments'];
                  if (commentsData != null) {
                    if (commentsData is List) {
                      comments += commentsData.length;
                    } else if (commentsData is Map) {
                      comments += commentsData.length;
                    }
                  }
                }
              } catch (e) {
                debugPrint('❌ خطأ في معالجة منشور: $e');
              }
            }
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحويل بيانات المنشورات: $e');
        }
      } else {
        debugPrint('⚠️ لا توجد منشورات في قاعدة البيانات');
      }

      debugPrint('📊 إحصائيات المنشورات: total=$total, likes=$likes, reactions=$reactions, comments=$comments');

      return {
        'total': total,
        'likes': likes,
        'reactions': reactions,
        'comments': comments,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات المنشورات: $e');
      return {
        'total': 0,
        'likes': 0,
        'reactions': 0,
        'comments': 0,
      };
    }
  }
  
  // حساب إحصائيات التفاعل (التعليقات وردود الفعل التي قدمها المستخدم)
  Future<Map<String, dynamic>> _calculateInteractionStatistics(String userId) async {
    try {
      debugPrint('🔍 بدء حساب إحصائيات التفاعل للمستخدم: $userId');
      final postsRef = _database.ref('posts');
      final snapshot = await postsRef.get();

      int commentsGiven = 0;
      int reactionsGiven = 0;

      if (snapshot.exists && snapshot.value != null) {
        try {
          final dynamic rawData = snapshot.value;

          if (rawData is Map) {
            final postsData = _convertToStringDynamicMap(rawData);

            for (final entry in postsData.entries) {
              try {
                // تحقق من أن القيمة ليست null
                if (entry.value == null) {
                  continue;
                }

                final postData = _convertToStringDynamicMap(entry.value);

                // حساب التعليقات المقدمة
                final commentsData = postData['comments'];
                if (commentsData != null) {
                  if (commentsData is List) {
                    // التعليقات مخزنة كـ List
                    for (final comment in commentsData) {
                      if (comment is Map && comment['authorId'] == userId) {
                        commentsGiven++;
                      }
                    }
                  } else if (commentsData is Map) {
                    // التعليقات مخزنة كـ Map
                    for (final comment in commentsData.values) {
                      if (comment is Map && comment['authorId'] == userId) {
                        commentsGiven++;
                      }
                    }
                  }
                }

                // حساب ردود الفعل المقدمة
                final reactionsData = postData['reactions'];
                if (reactionsData != null && reactionsData is Map) {
                  try {
                    // البنية الجديدة: reactions: { userIds: {like: [userId1, userId2], love: [userId3]} }
                    final userIdsData = reactionsData['userIds'];
                    if (userIdsData != null && userIdsData is Map) {
                      for (final reactionUsers in userIdsData.values) {
                        if (reactionUsers is List && reactionUsers.contains(userId)) {
                          reactionsGiven++;
                        }
                      }
                    } else {
                      // البنية القديمة: reactions: { like: { users: [...] }, love: { users: [...] } }
                      for (final reactionType in reactionsData.values) {
                        if (reactionType is Map) {
                          final usersList = reactionType['users'];
                          if (usersList != null) {
                            if (usersList is List && usersList.contains(userId)) {
                              reactionsGiven++;
                            } else if (usersList is Map && usersList.containsKey(userId)) {
                              reactionsGiven++;
                            }
                          }
                        }
                      }
                    }
                  } catch (e) {
                    debugPrint('❌ خطأ في معالجة ردود الفعل المقدمة: $e');
                  }
                }
              } catch (e) {
                debugPrint('❌ خطأ في معالجة تفاعل: $e');
              }
            }
          }
        } catch (e) {
          debugPrint('❌ خطأ في تحويل بيانات التفاعل: $e');
        }
      }

      debugPrint('📊 إحصائيات التفاعل: commentsGiven=$commentsGiven, reactionsGiven=$reactionsGiven');

      return {
        'commentsGiven': commentsGiven,
        'reactionsGiven': reactionsGiven,
      };
    } catch (e) {
      debugPrint('❌ خطأ في حساب إحصائيات التفاعل: $e');
      return {
        'commentsGiven': 0,
        'reactionsGiven': 0,
      };
    }
  }
  
  // حساب إحصائيات الوقت والنشاط
  Future<Map<String, dynamic>> _calculateTimeStatistics(String userId, UserModel user) async {
    try {
      // هذه الإحصائيات تحتاج لتتبع أكثر تفصيلاً في المستقبل
      // حالياً سنحسب تقديرات بناءً على البيانات المتاحة
      
      final now = DateTime.now();
      final daysSinceJoined = now.difference(user.createdAt).inDays + 1;
      final daysSinceLastActive = now.difference(user.lastActiveAt).inDays;
      
      // تقدير الجلسات والوقت (يمكن تحسينه لاحقاً بتتبع فعلي)
      final estimatedSessions = daysSinceJoined * 2; // تقدير جلستين يومياً
      final estimatedTotalMinutes = daysSinceJoined * 30; // تقدير 30 دقيقة يومياً
      final estimatedAvgSessionMinutes = estimatedTotalMinutes ~/ estimatedSessions;
      
      // تقدير الأيام النشطة
      final activeDays = daysSinceJoined - daysSinceLastActive;
      
      // إحصائيات بسيطة للنشاط (يمكن تطويرها لاحقاً)
      Map<String, int> byDay = {};
      Map<String, int> byMonth = {};
      
      return {
        'totalTime': Duration(minutes: estimatedTotalMinutes),
        'sessions': estimatedSessions,
        'avgSession': Duration(minutes: estimatedAvgSessionMinutes),
        'activeDays': activeDays > 0 ? activeDays : 1,
        'byDay': byDay,
        'byMonth': byMonth,
      };
    } catch (e) {
      debugPrint('خطأ في حساب إحصائيات الوقت: $e');
      return {
        'totalTime': Duration.zero,
        'sessions': 0,
        'avgSession': Duration.zero,
        'activeDays': 1,
        'byDay': <String, int>{},
        'byMonth': <String, int>{},
      };
    }
  }
  
  // دالة مساعدة لتحويل البيانات
  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      // تحويل آمن من Map<Object?, Object?> إلى Map<String, dynamic>
      final Map<String, dynamic> result = {};
      data.forEach((key, value) {
        if (key != null) {
          result[key.toString()] = _convertValue(value);
        }
      });
      return result;
    } else {
      throw Exception('البيانات ليست من نوع Map: ${data.runtimeType}');
    }
  }

  // دالة مساعدة لتحويل القيم المعقدة
  dynamic _convertValue(dynamic value) {
    if (value is Map) {
      // تحويل Map متداخل
      final Map<String, dynamic> result = {};
      value.forEach((key, val) {
        if (key != null) {
          result[key.toString()] = _convertValue(val);
        }
      });
      return result;
    } else if (value is List) {
      // تحويل List
      return value.map((item) => _convertValue(item)).toList();
    } else {
      // قيمة بسيطة
      return value;
    }
  }

  // مساعدات
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
  
  // مسح البيانات
  void clear() {
    _userStatistics = null;
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
