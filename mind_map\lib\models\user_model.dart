class UserModel {
  final String uid;
  final String email;
  final String firstName;
  final String lastName;
  final String university;
  final String major;
  final String bio; // النبذة الشخصية
  final DateTime birthDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastActiveAt; // آخر نشاط للمستخدم
  final List<String> following; // قائمة المستخدمين الذين يتابعهم
  final List<String> followers; // قائمة المتابعين
  final bool isAdmin; // هل المستخدم مدير

  UserModel({
    required this.uid,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.university,
    required this.major,
    this.bio = '', // النبذة الشخصية اختيارية
    required this.birthDate,
    required this.createdAt,
    required this.updatedAt,
    DateTime? lastActiveAt,
    List<String>? following,
    List<String>? followers,
    this.isAdmin = false, // افتراضياً ليس مدير
  }) : lastActiveAt = lastActiveAt ?? DateTime.now(),
       following = following ?? [],
       followers = followers ?? [];

  // حساب العمر تلقائياً
  int get age {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    // التحقق من عدم حلول عيد الميلاد بعد
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }

  // الاسم الكامل
  String get fullName => '$firstName $lastName';

  // حالة النشاط - يعتبر المستخدم نشط إذا كان آخر نشاط له خلال آخر 5 دقائق
  bool get isActive {
    final now = DateTime.now();
    final difference = now.difference(lastActiveAt);
    return difference.inMinutes <= 5;
  }

  // وقت عدم النشاط
  String get inactiveTime {
    if (isActive) return '';

    final now = DateTime.now();
    final difference = now.difference(lastActiveAt);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ساعة';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} يوم';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years سنة';
    }
  }

  // عدد المتابعين
  int get followersCount => followers.length;

  // عدد المتابَعين
  int get followingCount => following.length;

  // التحقق من متابعة مستخدم معين
  bool isFollowing(String userId) => following.contains(userId);

  // التحقق من كون مستخدم معين متابع
  bool isFollowedBy(String userId) => followers.contains(userId);

  // تحويل إلى Map للحفظ في Firebase
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'university': university,
      'major': major,
      'bio': bio,
      'birthDate': birthDate.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
      'following': following,
      'followers': followers,
      'isAdmin': isAdmin,
    };
  }

  // إنشاء من Map (من Firebase)
  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      university: map['university'] ?? '',
      major: map['major'] ?? '',
      bio: map['bio'] ?? '',
      birthDate: DateTime.parse(map['birthDate']),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      lastActiveAt: map['lastActiveAt'] != null
          ? DateTime.parse(map['lastActiveAt'])
          : DateTime.now(),
      following: List<String>.from(map['following'] ?? []),
      followers: List<String>.from(map['followers'] ?? []),
      isAdmin: map['isAdmin'] ?? false,
    );
  }

  // نسخ مع تعديل بعض الحقول
  UserModel copyWith({
    String? uid,
    String? email,
    String? firstName,
    String? lastName,
    String? university,
    String? major,
    String? bio,
    DateTime? birthDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastActiveAt,
    List<String>? following,
    List<String>? followers,
    bool? isAdmin,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      university: university ?? this.university,
      major: major ?? this.major,
      bio: bio ?? this.bio,
      birthDate: birthDate ?? this.birthDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
      following: following ?? List.from(this.following),
      followers: followers ?? List.from(this.followers),
      isAdmin: isAdmin ?? this.isAdmin,
    );
  }

  @override
  String toString() {
    return 'UserModel(uid: $uid, email: $email, fullName: $fullName, university: $university, major: $major, age: $age)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel &&
      other.uid == uid &&
      other.email == email &&
      other.firstName == firstName &&
      other.lastName == lastName &&
      other.university == university &&
      other.major == major &&
      other.birthDate == birthDate;
  }

  @override
  int get hashCode {
    return uid.hashCode ^
      email.hashCode ^
      firstName.hashCode ^
      lastName.hashCode ^
      university.hashCode ^
      major.hashCode ^
      birthDate.hashCode;
  }
}
