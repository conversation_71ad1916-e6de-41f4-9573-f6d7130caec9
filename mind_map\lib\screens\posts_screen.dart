import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/posts_provider.dart';
import '../widgets/post_card.dart';

class PostsScreen extends StatefulWidget {
  final String? highlightPostId;
  final String? highlightCommentId;

  const PostsScreen({
    Key? key,
    this.highlightPostId,
    this.highlightCommentId,
  }) : super(key: key);

  @override
  State<PostsScreen> createState() => _PostsScreenState();
}

class _PostsScreenState extends State<PostsScreen> {
  Timer? _announcementTimer;
  String _currentAnnouncement = '';
  bool _showCheckIcon = false;

  @override
  void initState() {
    super.initState();
    _startAnnouncementTimer();

    // تحميل المنشورات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PostsProvider>().loadPosts();
    });
  }

  @override
  void dispose() {
    _announcementTimer?.cancel();
    super.dispose();
  }

  // بدء مؤقت التحديث التلقائي
  void _startAnnouncementTimer() {
    _announcementTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (mounted) {
        _updateAnnouncement();
      }
    });
    // تحديث فوري عند البداية
    _updateAnnouncement();
  }

  // تحديث نص الإعلان
  void _updateAnnouncement() {
    final postsProvider = context.read<PostsProvider>();
    if (postsProvider.posts.isNotEmpty) {
      final latestPost = postsProvider.posts.first;
      final now = DateTime.now();
      final timeDiff = now.difference(latestPost.createdAt);

      String newAnnouncement;
      if (timeDiff.inMinutes < 1) {
        newAnnouncement = 'تم نشر منشور جديد الآن بواسطة ${latestPost.authorName}';
      } else if (timeDiff.inMinutes < 60) {
        newAnnouncement = 'تم نشر منشور منذ ${timeDiff.inMinutes} دقيقة بواسطة ${latestPost.authorName}';
      } else if (timeDiff.inHours < 24) {
        newAnnouncement = 'تم نشر منشور منذ ${timeDiff.inHours} ساعة بواسطة ${latestPost.authorName}';
      } else {
        newAnnouncement = 'آخر منشور تم نشره بواسطة ${latestPost.authorName}';
      }

      if (mounted && _currentAnnouncement != newAnnouncement) {
        setState(() {
          _currentAnnouncement = newAnnouncement;
          _showCheckIcon = timeDiff.inMinutes < 5; // إظهار علامة الصح للمنشورات الحديثة
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PostsProvider>(
      builder: (context, postsProvider, child) {
        // تحديث الإعلان عند تغيير المنشورات
        if (postsProvider.posts.isNotEmpty && _currentAnnouncement.isEmpty) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _updateAnnouncement();
          });
        }

        if (postsProvider.isLoading) {
          return _buildLoadingScreen();
        }

        if (postsProvider.error != null) {
          return _buildErrorScreen(postsProvider);
        }

        if (postsProvider.posts.isEmpty) {
          return _buildEmptyScreen();
        }

        return _buildPostsScreen(postsProvider);
      },
    );
  }

  Widget _buildLoadingScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
              strokeWidth: 3,
            ),
            const SizedBox(height: 24),
            Text(
              'جاري تحميل المنشورات...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى الانتظار',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorScreen(PostsProvider postsProvider) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red.shade400,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'خطأ في تحميل المنشورات',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                postsProvider.error!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () => postsProvider.loadPosts(),
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyScreen() {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.post_add,
                  size: 80,
                  color: Colors.blue.shade300,
                ),
              ),
              const SizedBox(height: 32),
              Text(
                'لا توجد منشورات بعد',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'كن أول من يشارك مخططاً ذهنياً مع المجتمع!',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade500,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () {
                  // الانتقال إلى شاشة المشاريع
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.add_circle_outline),
                label: const Text('إنشاء مخطط ذهني'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              OutlinedButton.icon(
                onPressed: () => context.read<PostsProvider>().loadPosts(),
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue.shade600,
                  side: BorderSide(color: Colors.blue.shade600),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPostsScreen(PostsProvider postsProvider) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      body: LayoutBuilder(
        builder: (context, constraints) {
          // تحديد نوع التخطيط حسب عرض الشاشة
          final isTablet = constraints.maxWidth > 768;
          final isDesktop = constraints.maxWidth > 1200;

          return RefreshIndicator(
            onRefresh: () => postsProvider.loadPosts(),
            color: Colors.blue.shade600,
            child: _buildResponsiveLayout(postsProvider, constraints, isTablet, isDesktop),
          );
        },
      ),
    );
  }

  Widget _buildResponsiveLayout(
    PostsProvider postsProvider,
    BoxConstraints constraints,
    bool isTablet,
    bool isDesktop
  ) {
    if (isDesktop) {
      return _buildDesktopLayout(postsProvider, constraints);
    } else if (isTablet) {
      return _buildTabletLayout(postsProvider, constraints);
    } else {
      return _buildMobileLayout(postsProvider, constraints);
    }
  }

  // تخطيط الهاتف المحمول
  Widget _buildMobileLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return CustomScrollView(
      slivers: [
        // شريط الإعلانات
        _buildAnnouncementBar(postsProvider),

        // قائمة المنشورات
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final post = postsProvider.posts[index];
              return Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: _getHorizontalPadding(constraints),
                  vertical: 8,
                ),
                child: PostCard(
                  post: post,
                  highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
                ),
              );
            },
            childCount: postsProvider.posts.length,
          ),
        ),

        // مساحة إضافية في النهاية
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // تخطيط الجهاز اللوحي
  Widget _buildTabletLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return CustomScrollView(
      slivers: [
        // شريط الإعلانات
        _buildAnnouncementBar(postsProvider),

        // قائمة المنشورات بتخطيط شبكي للأجهزة اللوحية
        SliverPadding(
          padding: EdgeInsets.symmetric(
            horizontal: _getHorizontalPadding(constraints),
          ),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final post = postsProvider.posts[index];
                return PostCard(
                  post: post,
                  highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
                );
              },
              childCount: postsProvider.posts.length,
            ),
          ),
        ),

        // مساحة إضافية في النهاية
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  // تخطيط سطح المكتب
  Widget _buildDesktopLayout(PostsProvider postsProvider, BoxConstraints constraints) {
    return Row(
      children: [
        // الشريط الجانبي للإحصائيات (اختياري)
        if (constraints.maxWidth > 1400) _buildSidebar(postsProvider),

        // المحتوى الرئيسي
        Expanded(
          child: CustomScrollView(
            slivers: [
              // شريط الإعلانات
              _buildAnnouncementBar(postsProvider),

              // قائمة المنشورات بتخطيط شبكي للسطح المكتب
              SliverPadding(
                padding: EdgeInsets.symmetric(
                  horizontal: _getHorizontalPadding(constraints),
                ),
                sliver: SliverGrid(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: constraints.maxWidth > 1600 ? 3 : 2,
                    childAspectRatio: 0.75,
                    crossAxisSpacing: 20,
                    mainAxisSpacing: 20,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final post = postsProvider.posts[index];
                      return PostCard(
                        post: post,
                        highlightCommentId: widget.highlightPostId == post.id ? widget.highlightCommentId : null,
                      );
                    },
                    childCount: postsProvider.posts.length,
                  ),
                ),
              ),

              // مساحة إضافية في النهاية
              const SliverToBoxAdapter(
                child: SizedBox(height: 100),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // حساب المسافة الأفقية حسب حجم الشاشة
  double _getHorizontalPadding(BoxConstraints constraints) {
    if (constraints.maxWidth > 1200) {
      return constraints.maxWidth * 0.1; // 10% من عرض الشاشة للشاشات الكبيرة
    } else if (constraints.maxWidth > 768) {
      return 32.0; // مسافة ثابتة للأجهزة اللوحية
    } else {
      return 16.0; // مسافة صغيرة للهواتف
    }
  }

  // الشريط الجانبي للشاشات الكبيرة جداً
  Widget _buildSidebar(PostsProvider postsProvider) {
    return Container(
      width: 280,
      color: Colors.white,
      child: Column(
        children: [
          // إحصائيات سريعة
          Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'إحصائيات سريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildStatItem(
                  'إجمالي المنشورات',
                  '${postsProvider.posts.length}',
                  Icons.article,
                  Colors.blue,
                ),
                const SizedBox(height: 12),
                _buildStatItem(
                  'المنشورات اليوم',
                  '${_getTodayPostsCount(postsProvider)}',
                  Icons.today,
                  Colors.green,
                ),
                const SizedBox(height: 12),
                _buildStatItem(
                  'المنشورات الشائعة',
                  '${_getPopularPostsCount(postsProvider)}',
                  Icons.trending_up,
                  Colors.orange,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _getTodayPostsCount(PostsProvider postsProvider) {
    final today = DateTime.now();
    return postsProvider.posts.where((post) {
      return post.createdAt.year == today.year &&
             post.createdAt.month == today.month &&
             post.createdAt.day == today.day;
    }).length;
  }

  int _getPopularPostsCount(PostsProvider postsProvider) {
    return postsProvider.posts.where((post) {
      final totalInteractions = post.likes.length +
                               post.reactions.totalCount +
                               post.comments.length;
      return totalInteractions >= 5;
    }).length;
  }

  // بناء شريط الإعلانات
  Widget _buildAnnouncementBar(PostsProvider postsProvider) {
    // إذا لم توجد منشورات، لا تعرض الشريط
    if (postsProvider.posts.isEmpty || _currentAnnouncement.isEmpty) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    return _buildAnnouncementBarContent(
      title: _showCheckIcon ? 'منشور جديد!' : 'آخر الأنشطة',
      subtitle: _currentAnnouncement,
      icon: _showCheckIcon ? Icons.campaign_rounded : Icons.schedule_rounded,
      isNew: _showCheckIcon,
      onTap: () {
        setState(() {
          _showCheckIcon = false;
        });
      },
    );
  }

  // بناء محتوى شريط الإعلانات
  Widget _buildAnnouncementBarContent({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isNew,
    required VoidCallback onTap,
  }) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isNew
                ? [Colors.green.shade400, Colors.green.shade600]
                : [Colors.blue.shade400, Colors.blue.shade600],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: (isNew ? Colors.green : Colors.blue).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // أيقونة الإعلان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // نص الإعلان
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),

                  // علامات الحالة
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // علامة الصح للمنشورات الجديدة
                      if (isNew)
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.check_circle_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),

                      const SizedBox(width: 8),

                      // علامة X للمنشورات القديمة أو زر التحديث
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          isNew ? Icons.refresh_rounded : Icons.schedule_rounded,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }


}
