<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":share_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\share_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":open_file_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\open_file_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\firebase_storage\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_database" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\firebase_database\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\firebase_auth\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\file_picker\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":cloud_firestore" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\cloud_firestore\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\mind_map_univ\mind_map\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>