import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/post.dart';
import '../models/mind_map.dart';
import '../models/reaction.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/edit_requests_provider.dart';
import '../screens/mind_map_editor_screen.dart';
import 'mind_map_preview_painter.dart';
import 'add_comment_widget.dart';
import 'reaction_picker.dart';
import 'reply_dialog.dart';


class PostCard extends StatefulWidget {
  final Post post;
  final String? highlightCommentId;

  const PostCard({
    super.key,
    required this.post,
    this.highlightCommentId,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> with TickerProviderStateMixin {
  late AnimationController _highlightController;
  late Animation<Color?> _highlightAnimation;
  String? _currentHighlightId;

  @override
  void initState() {
    super.initState();
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _highlightAnimation = ColorTween(
      begin: Colors.transparent,
      end: Colors.amber.withValues(alpha: 0.4),
    ).animate(CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeInOut,
    ));

    // بدء التمييز إذا كان هناك تعليق محدد
    if (widget.highlightCommentId != null) {
      _startHighlight(widget.highlightCommentId!);
    }
  }

  @override
  void dispose() {
    _highlightController.dispose();
    super.dispose();
  }

  void _startHighlight(String commentId) {
    setState(() {
      _currentHighlightId = commentId;
    });

    // بدء الأنيميشن
    _highlightController.forward();

    // إيقاف التمييز بعد 5 ثوانٍ
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        _highlightController.reverse().then((_) {
          if (mounted) {
            setState(() {
              _currentHighlightId = null;
            });
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس المنشور - معلومات المؤلف
          _buildPostHeader(context),

          // محتوى المنشور
          if (widget.post.description.isNotEmpty) _buildPostContent(context),

          // معاينة المخطط الذهني
          _buildMindMapPreview(context),

          // إحصائيات الإعجابات والتعليقات
          _buildPostStats(context),

          // خط فاصل
          Container(
            height: 1,
            color: Colors.grey.shade200,
            margin: const EdgeInsets.symmetric(horizontal: 16),
          ),

          // أزرار التفاعل
          _buildInteractionButtons(context),

          // التعليقات مدمجة في نفس البطاقة
          _buildCommentsSection(context),
        ],
      ),
    );
  }

  Widget _buildPostHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // أيقونة المستخدم
          CircleAvatar(
            backgroundColor: Colors.blue.shade100,
            child: Text(
              _getAuthorInitial(widget.post.authorName),
              style: TextStyle(
                color: Colors.blue.shade700,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // معلومات المؤلف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.post.authorName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  widget.post.authorUniversity,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                // عرض معلومات المحرر إن وجد
                if (widget.post.editedByUserName != null) ...[
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Icon(
                        Icons.edit,
                        size: 14,
                        color: Colors.blue.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تم تعديله بواسطة ${widget.post.editedByUserName}',
                        style: TextStyle(
                          color: Colors.blue.shade600,
                          fontSize: 12,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 2),
                Text(
                  _formatTimeAgo(widget.post.createdAt),
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          // قائمة الخيارات
          _buildPostMenu(context),
        ],
      ),
    );
  }

  Widget _buildPostContent(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان المخطط
          Row(
            children: [
              const Icon(
                Icons.account_tree,
                color: Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.post.mindMapTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          // المادة الدراسية
          if (widget.post.mindMapSubject.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                widget.post.mindMapSubject,
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
          
          // وصف المنشور
          if (widget.post.description.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              widget.post.description,
              style: const TextStyle(
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMindMapPreview(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // معاينة المخطط
            Positioned.fill(
              child: CustomPaint(
                painter: MindMapPreviewPainter(
                  mindMap: MindMap.fromJson(widget.post.mindMapData),
                ),
              ),
            ),
            
            // زر فتح المخطط
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.9),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: () => _openMindMap(context),
                  icon: const Icon(Icons.open_in_new),
                  tooltip: 'فتح المخطط',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionButtons(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUserId = authProvider.user?.uid;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // زر ردود الفعل
          ReactionButton(
            reactions: widget.post.reactions,
            currentUserId: currentUserId,
            onReactionTap: (reactionType) => _toggleReaction(context, reactionType),
            onLongPress: () => _showReactionPicker(context),
          ),
          
          const SizedBox(width: 16),
          
          // زر التعليقات
          InkWell(
            onTap: () {
              // التمرير إلى مربع إضافة التعليق (لا حاجة لشاشة منفصلة)
            },
            borderRadius: BorderRadius.circular(20),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.comment_outlined,
                    color: Colors.grey.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.post.commentsCount}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildPostMenu(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    final currentUserId = authProvider.user?.uid;
    final isOwner = currentUserId == widget.post.authorId;

    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'edit':
            _editPost(context);
            break;
          case 'delete':
            _deletePost(context);
            break;
          case 'request_edit':
            _requestEdit(context);
            break;
        }
      },
      itemBuilder: (context) => [
        if (isOwner) ...[
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit, color: Colors.blue),
              title: Text('تعديل المنشور'),
            ),
          ),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('حذف المنشور'),
            ),
          ),
        ] else ...[
          const PopupMenuItem(
            value: 'request_edit',
            child: ListTile(
              leading: Icon(Icons.edit_note, color: Colors.blue),
              title: Text('طلب التعديل'),
            ),
          ),
        ],
      ],
    );
  }

  // فتح المخطط الذهني
  void _openMindMap(BuildContext context) {
    final authProvider = context.read<AuthProvider>();
    final currentUserId = authProvider.user?.uid;
    final isOwner = currentUserId == widget.post.authorId;

    final mindMap = MindMap.fromJson(widget.post.mindMapData);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MindMapEditorScreen(
          mindMap: mindMap,
          isReadOnly: !isOwner, // وضع القراءة فقط للمستخدمين الآخرين
        ),
      ),
    );
  }

  // تبديل رد الفعل
  void _toggleReaction(BuildContext context, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;
    final userModel = authProvider.userModel;

    if (userId == null || userModel == null) return;

    postsProvider.togglePostReaction(widget.post.id, userId, reactionType, user: userModel);
  }

  // عرض منتقي ردود الفعل
  void _showReactionPicker(BuildContext context) {
    showReactionPicker(
      context: context,
      currentReaction: widget.post.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleReaction(context, reactionType),
    );
  }



  // تعديل المنشور
  void _editPost(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => EditPostDialog(post: widget.post),
    );
  }

  // طلب التعديل
  void _requestEdit(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => RequestEditDialog(post: widget.post),
    );
  }

  // حذف المنشور
  void _deletePost(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنشور'),
        content: const Text('هل أنت متأكد من حذف هذا المنشور؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<PostsProvider>().deletePost(widget.post.id);
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // إحصائيات المنشور (الإعجابات والتعليقات)
  Widget _buildPostStats(BuildContext context) {
    if (widget.post.likesCount == 0 && widget.post.comments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // إحصائيات الإعجابات
          if (widget.post.likesCount > 0)
            Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.thumb_up,
                    color: Colors.white,
                    size: 12,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  '${widget.post.likesCount}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),

          // إحصائيات التعليقات
          if (widget.post.comments.isNotEmpty)
            Text(
              '${widget.post.comments.length} تعليق',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
        ],
      ),
    );
  }

  // قسم التعليقات المدمج
  Widget _buildCommentsSection(BuildContext context) {
    return Column(
      children: [
        // التعليقات الموجودة
        if (widget.post.comments.isNotEmpty) _buildCommentsList(context),

        // مربع إضافة تعليق جديد
        _buildAddCommentBox(context),
      ],
    );
  }

  // قائمة التعليقات
  Widget _buildCommentsList(BuildContext context) {
    // ترتيب التعليقات من الأحدث إلى الأقدم
    final sortedComments = List<PostComment>.from(widget.post.comments)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // عرض أول 5 تعليقات فقط
    final commentsToShow = sortedComments.take(5).toList();
    final hasMoreComments = sortedComments.length > 5;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // عرض التعليقات المحددة
          ...commentsToShow.map((comment) => _buildCommentItem(context, comment)),

          // زر عرض المزيد من التعليقات
          if (hasMoreComments)
            GestureDetector(
              onTap: () => _showAllComments(context),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  'عرض ${sortedComments.length - 5} تعليقات أخرى',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // عرض جميع التعليقات في حوار منبثق
  void _showAllComments(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'التعليقات (${widget.post.comments.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // قائمة التعليقات القابلة للتمرير
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: widget.post.comments.length,
                  itemBuilder: (context, index) {
                    // ترتيب التعليقات من الأحدث إلى الأقدم
                    final sortedComments = List<PostComment>.from(widget.post.comments)
                      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
                    final comment = sortedComments[index];

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _buildCommentItem(context, comment),
                    );
                  },
                ),
              ),

              // مربع إضافة تعليق في الأسفل
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    top: BorderSide(color: Colors.grey.shade200),
                  ),
                ),
                child: AddCommentWidget(post: widget.post),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عنصر تعليق واحد
  Widget _buildCommentItem(BuildContext context, PostComment comment) {
    final isHighlighted = _currentHighlightId == comment.id;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 18,
            backgroundColor: Colors.grey.shade300,
            child: Text(
              _getAuthorInitial(comment.authorName),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 10),

          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AnimatedBuilder(
                  animation: _highlightAnimation,
                  builder: (context, child) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isHighlighted
                            ? Color.lerp(Colors.grey.shade100, _highlightAnimation.value, 0.7)
                            : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(16),
                        border: isHighlighted
                            ? Border.all(color: Colors.amber, width: 2)
                            : null,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            comment.authorName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 13,
                              color: isHighlighted ? Colors.amber.shade800 : Colors.black,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            comment.content,
                            style: TextStyle(
                              fontSize: 14,
                              color: isHighlighted ? Colors.amber.shade700 : Colors.black,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                // أزرار التفاعل مع التعليق
                Padding(
                  padding: const EdgeInsets.only(top: 4, right: 12),
                  child: Row(
                    children: [
                      Text(
                        _formatTime(comment.createdAt),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(width: 16),

                      // زر ردود الفعل للتعليق
                      ReactionButton(
                        reactions: comment.reactions,
                        currentUserId: context.read<AuthProvider>().user?.uid,
                        onReactionTap: (reactionType) => _toggleCommentReaction(context, comment, reactionType),
                        onLongPress: () => _showCommentReactionPicker(context, comment),
                      ),

                      const SizedBox(width: 16),
                      GestureDetector(
                        onTap: () => _showReplyDialog(context, comment),
                        child: Text(
                          'رد',
                          style: TextStyle(
                            color: Colors.blue.shade600,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // عرض الردود إذا وجدت
                if (comment.hasReplies) _buildRepliesSection(context, comment),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // مربع إضافة تعليق جديد
  Widget _buildAddCommentBox(BuildContext context) {
    return AddCommentWidget(post: widget.post);
  }

  // تبديل رد الفعل على التعليق
  void _toggleCommentReaction(BuildContext context, PostComment comment, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;
    final userModel = authProvider.userModel;

    if (userId == null || userModel == null) return;

    postsProvider.toggleCommentReaction(widget.post.id, comment.id, userId, reactionType, user: userModel);
  }

  // عرض منتقي ردود الفعل للتعليق
  void _showCommentReactionPicker(BuildContext context, PostComment comment) {
    showReactionPicker(
      context: context,
      currentReaction: comment.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleCommentReaction(context, comment, reactionType),
    );
  }

  // عرض حوار الرد على التعليق
  void _showReplyDialog(BuildContext context, PostComment comment) {
    print('🔄 فتح حوار الرد للتعليق: ${comment.id}');
    showDialog(
      context: context,
      builder: (context) => ReplyDialog(
        post: widget.post,
        parentComment: comment,
      ),
    );
  }

  // بناء قسم الردود
  Widget _buildRepliesSection(BuildContext context, PostComment comment) {
    // عرض أول 3 ردود فقط
    final repliesToShow = comment.replies.take(3).toList();
    final hasMoreReplies = comment.replies.length > 3;

    return Container(
      margin: const EdgeInsets.only(top: 8, right: 40),
      child: Column(
        children: [
          // عرض الردود
          ...repliesToShow.map((reply) => _buildReplyItem(context, reply)),

          // زر عرض المزيد من الردود
          if (hasMoreReplies)
            GestureDetector(
              onTap: () => _showAllReplies(context, comment),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Text(
                  'عرض ${comment.replies.length - 3} ردود أخرى',
                  style: TextStyle(
                    color: Colors.blue.shade600,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // بناء عنصر رد واحد
  Widget _buildReplyItem(BuildContext context, PostComment reply) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم للرد
          CircleAvatar(
            radius: 14,
            backgroundColor: Colors.grey.shade300,
            child: Text(
              _getAuthorInitial(reply.authorName),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 8),

          // محتوى الرد
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reply.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        reply.content,
                        style: const TextStyle(fontSize: 13),
                      ),
                    ],
                  ),
                ),

                // أزرار التفاعل مع الرد
                Padding(
                  padding: const EdgeInsets.only(top: 2, right: 10),
                  child: Row(
                    children: [
                      Text(
                        _formatTime(reply.createdAt),
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 11,
                        ),
                      ),
                      const SizedBox(width: 12),

                      // زر ردود الفعل للرد
                      ReactionButton(
                        reactions: reply.reactions,
                        currentUserId: context.read<AuthProvider>().user?.uid,
                        onReactionTap: (reactionType) => _toggleReplyReaction(context, reply, reactionType),
                        onLongPress: () => _showReplyReactionPicker(context, reply),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // عرض جميع الردود
  void _showAllReplies(BuildContext context, PostComment comment) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        minChildSize: 0.4,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 8),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // عنوان
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Text(
                      'الردود (${comment.replies.length})',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(height: 1),

              // قائمة الردود القابلة للتمرير
              Expanded(
                child: ListView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: comment.replies.length,
                  itemBuilder: (context, index) {
                    final reply = comment.replies[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildReplyItem(context, reply),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // تبديل رد الفعل على الرد
  void _toggleReplyReaction(BuildContext context, PostComment reply, ReactionType reactionType) {
    final authProvider = context.read<AuthProvider>();
    final postsProvider = context.read<PostsProvider>();
    final userId = authProvider.user?.uid;

    if (userId == null) return;

    postsProvider.toggleReplyReaction(widget.post.id, reply.parentCommentId!, reply.id, userId, reactionType);
  }

  // عرض منتقي ردود الفعل للرد
  void _showReplyReactionPicker(BuildContext context, PostComment reply) {
    showReactionPicker(
      context: context,
      currentReaction: reply.reactions.getUserReaction(
        context.read<AuthProvider>().user?.uid ?? ''
      ),
      onReactionSelected: (reactionType) => _toggleReplyReaction(context, reply, reactionType),
    );
  }

  // تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  // تنسيق الوقت
  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  String _getAuthorInitial(String authorName) {
    if (authorName.trim().isEmpty) {
      return 'م'; // مستخدم
    }

    // إزالة المسافات الزائدة والحصول على الحرف الأول
    final trimmedName = authorName.trim();
    return trimmedName[0].toUpperCase();
  }
}

// حوار تعديل المنشور
class EditPostDialog extends StatefulWidget {
  final Post post;

  const EditPostDialog({
    super.key,
    required this.post,
  });

  @override
  State<EditPostDialog> createState() => _EditPostDialogState();
}

class _EditPostDialogState extends State<EditPostDialog> {
  late TextEditingController _descriptionController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController(text: widget.post.description);
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text(
        'تعديل المنشور',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عرض عنوان المخطط الذهني (غير قابل للتعديل)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'المخطط الذهني:',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    widget.post.mindMapTitle,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // حقل الوصف
            const Text(
              'الوصف',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: InputDecoration(
                hintText: 'أدخل وصف المنشور (اختياري)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updatePost,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ التغييرات'),
        ),
      ],
    );
  }

  void _updatePost() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final postsProvider = context.read<PostsProvider>();

      await postsProvider.updatePostDescription(
        widget.post.id,
        _descriptionController.text.trim(),
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المنشور بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المنشور: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

// حوار طلب التعديل
class RequestEditDialog extends StatefulWidget {
  final Post post;

  const RequestEditDialog({
    super.key,
    required this.post,
  });

  @override
  State<RequestEditDialog> createState() => _RequestEditDialogState();
}

class _RequestEditDialogState extends State<RequestEditDialog> {
  final _messageController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('طلب التعديل'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المنشور
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المخطط: ${widget.post.mindMapTitle}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (widget.post.mindMapSubject.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text('المادة: ${widget.post.mindMapSubject}'),
                  ],
                  const SizedBox(height: 4),
                  Text('المؤلف: ${widget.post.authorName}'),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // رسالة الطلب
            const Text(
              'رسالة الطلب:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                hintText: 'اكتب رسالة توضح سبب طلب التعديل...',
                border: OutlineInputBorder(),
              ),
              maxLines: 4,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _sendRequest,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('إرسال الطلب'),
        ),
      ],
    );
  }

  void _sendRequest() async {
    final message = _messageController.text.trim();
    if (message.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة رسالة الطلب')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final authProvider = context.read<AuthProvider>();
      final editRequestsProvider = context.read<EditRequestsProvider>();
      final currentUser = authProvider.userModel;

      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      await editRequestsProvider.sendEditRequest(
        fromUser: currentUser,
        toUserId: widget.post.authorId,
        toUserName: widget.post.authorName,
        postId: widget.post.id,
        mindMapId: widget.post.mindMapId,
        mindMapTitle: widget.post.mindMapTitle,
        mindMapSubject: widget.post.mindMapSubject,
        requestMessage: message,
      );

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إرسال طلب التعديل بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إرسال الطلب: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
