import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/mind_map_provider.dart';
import '../providers/posts_provider.dart';
import '../providers/auth_provider.dart';
import '../providers/notifications_provider.dart';
import '../providers/edit_requests_provider.dart';
import '../models/mind_map.dart';
import '../models/subject.dart';
import 'mind_map_editor_screen.dart';
import 'subject_detail_screen.dart';
import 'profile_screen.dart';
import 'users_search_screen.dart';
import 'posts_screen.dart';
import 'notifications_screen.dart';
import 'edit_requests_screen.dart';
import 'admin_dashboard_screen.dart';
import '../widgets/mind_map_card.dart';
import '../widgets/subject_card.dart';

import '../widgets/create_mind_map_dialog.dart';
import '../widgets/create_subject_dialog.dart';
import '../widgets/edit_mind_map_dialog.dart';
import '../widgets/edit_subject_dialog.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  int _currentBottomNavIndex = 1; // البدء بشاشة "مشاريعي"

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // تحديث النص عند تغيير التبويبة
    });

    // تهيئة PostsProvider مع المستخدم الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = context.read<AuthProvider>();
      final postsProvider = context.read<PostsProvider>();
      final notificationsProvider = context.read<NotificationsProvider>();

      // الاستماع لتغييرات حالة المصادقة لإلغاء الاستماعات عند تسجيل الخروج
      authProvider.addListener(() {
        if (!authProvider.isSignedIn) {
          // تم تسجيل الخروج، إلغاء جميع الاستماعات
          notificationsProvider.setCurrentUser(null);
        }
      });


      final editRequestsProvider = context.read<EditRequestsProvider>();

      if (authProvider.user != null) {
        postsProvider.setCurrentUser(authProvider.user!.uid);
        notificationsProvider.setCurrentUser(authProvider.user!.uid);

        // تحميل طلبات التعديل مع تأخير
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            editRequestsProvider.loadReceivedRequests(authProvider.user!.uid);
            editRequestsProvider.loadSentRequests(authProvider.user!.uid);
          }
        });

        // ربط إشعارات المتابعة
        authProvider.onUserFollowed = (targetUserId, follower) {
          notificationsProvider.createFollowNotification(
            targetUserId: targetUserId,
            follower: follower,
          );
        };

        // ربط إشعارات المنشورات الجديدة
        postsProvider.onNewPost = (followerIds, author, postId, postTitle) {
          notificationsProvider.createNewPostNotification(
            followerIds: followerIds,
            author: author,
            postId: postId,
            postTitle: postTitle,
          );
        };

        // ربط إشعارات ردود الفعل على المنشورات
        postsProvider.onPostReacted = (postAuthorId, reactor, postId, postTitle, reactionType) {
          notificationsProvider.createReactionNotification(
            postAuthorId: postAuthorId,
            reactor: reactor,
            postId: postId,
            postTitle: postTitle,
            reactionType: reactionType,
          );
        };

        // ربط إشعارات التعليقات
        postsProvider.onPostCommented = (postAuthorId, commenter, postId, postTitle, commentId) {
          notificationsProvider.createCommentNotification(
            postAuthorId: postAuthorId,
            commenter: commenter,
            postId: postId,
            postTitle: postTitle,
            commentId: commentId,
          );
        };

        // ربط إشعارات الردود
        postsProvider.onCommentReplied = (commentAuthorId, replier, postId, commentId) {
          notificationsProvider.createReplyNotification(
            commentAuthorId: commentAuthorId,
            replier: replier,
            postId: postId,
            commentId: commentId,
          );
        };

        // ربط إشعارات ردود الفعل على التعليقات
        postsProvider.onCommentReacted = (commentAuthorId, reactor, postId, commentId, reactionType) {
          notificationsProvider.createCommentReactionNotification(
            commentAuthorId: commentAuthorId,
            reactor: reactor,
            postId: postId,
            commentId: commentId,
            reactionType: reactionType,
          );
        };
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }





  @override
  Widget build(BuildContext context) {
    // التحقق من المدير وتوجيهه للوحة التحكم
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isAdmin) {
          // إذا كان المستخدم مدير، توجيهه للوحة التحكم بعد 7 ثوانٍ
          WidgetsBinding.instance.addPostFrameCallback((_) {
            final navigator = Navigator.of(context);
            Future.delayed(const Duration(seconds: 7), () {
              if (mounted) {
                navigator.pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const AdminDashboardScreen(),
                  ),
                );
              }
            });
          });
          // عرض شاشة تحميل مؤقتة محسنة
          return Scaffold(
            body: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.indigo.shade600,
                    Colors.indigo.shade800,
                  ],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // أيقونة المدير مع تأثير النبض
                    TweenAnimationBuilder<double>(
                      tween: Tween(begin: 0.8, end: 1.2),
                      duration: const Duration(milliseconds: 1000),
                      builder: (context, scale, child) {
                        return Transform.scale(
                          scale: scale,
                          child: Container(
                            width: 100,
                            height: 100,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.admin_panel_settings_rounded,
                              color: Colors.white,
                              size: 50,
                            ),
                          ),
                        );
                      },
                      onEnd: () {
                        // إعادة تشغيل الأنيميشن
                        if (mounted) {
                          setState(() {});
                        }
                      },
                    ),

                    const SizedBox(height: 30),

                    // نص الترحيب
                    const Text(
                      'مرحباً بك',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),

                    const SizedBox(height: 8),

                    const Text(
                      'جاري تحضير لوحة التحكم الإدارية...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 40),

                    // مؤشر التحميل المخصص
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // نقاط متحركة
                    TweenAnimationBuilder<double>(
                      tween: Tween(begin: 0.0, end: 1.0),
                      duration: const Duration(milliseconds: 1500),
                      builder: (context, value, child) {
                        return Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: List.generate(3, (index) {
                            final delay = index * 0.3;
                            final animValue = (value - delay).clamp(0.0, 1.0);
                            return Container(
                              margin: const EdgeInsets.symmetric(horizontal: 4),
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(
                                  alpha: 0.3 + (animValue * 0.7),
                                ),
                                shape: BoxShape.circle,
                              ),
                            );
                          }),
                        );
                      },
                      onEnd: () {
                        // إعادة تشغيل أنيميشن النقاط
                        if (mounted) {
                          setState(() {});
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        // إذا لم يكن مدير، عرض الشاشة العادية
        return _buildNormalUserScreen();
      },
    );
  }

  Widget _buildNormalUserScreen() {
    return Scaffold(
      body: _buildCurrentScreen(),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed, // لإظهار جميع الأيقونات
        currentIndex: _currentBottomNavIndex,
        onTap: (index) {
          setState(() {
            _currentBottomNavIndex = index;
          });
        },
        items: [
          const BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.folder),
            label: 'مشاريعي',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'البحث',
          ),
          BottomNavigationBarItem(
            icon: Consumer<NotificationsProvider>(
              builder: (context, notificationsProvider, child) {
                return Stack(
                  children: [
                    const Icon(Icons.notifications),
                    if (notificationsProvider.hasUnreadNotifications)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            '${notificationsProvider.unreadCount}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                );
              },
            ),
            label: 'الإشعارات',
          ),
          const BottomNavigationBarItem(
            icon: Icon(Icons.person),
            label: 'بروفايلي',
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentScreen() {
    switch (_currentBottomNavIndex) {
      case 0:
        return _buildMainScreen();
      case 1:
        return _buildProjectsScreen();
      case 2:
        return _buildSearchScreen();
      case 3:
        return _buildNotificationsScreen();
      case 4:
        return _buildProfileScreen();
      default:
        return _buildProjectsScreen();
    }
  }

  Widget _buildMainScreen() {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'الرئيسية',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor: Colors.blue.shade700,
          foregroundColor: Colors.white,
          elevation: 0,
          bottom: TabBar(
            indicatorColor: Colors.white,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
            tabs: [
              const Tab(
                icon: Icon(Icons.article),
                text: 'المنشورات',
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.edit_note),
                    const SizedBox(width: 4),
                    const Text('الطلبات'),
                    FutureBuilder(
                      future: Future.delayed(const Duration(milliseconds: 100)),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState != ConnectionState.done) {
                          return const SizedBox.shrink();
                        }
                        return Consumer<EditRequestsProvider>(
                          builder: (context, provider, child) {
                            final count = provider.unreadReceivedRequestsCount;
                            if (count > 0) {
                              return Container(
                                margin: const EdgeInsets.only(left: 4),
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  count.toString(),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              );
                            }
                            return const SizedBox.shrink();
                          },
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            PostsScreen(),
            EditRequestsScreen(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchScreen() {
    return const UsersSearchScreen();
  }

  Widget _buildNotificationsScreen() {
    return const NotificationsScreen();
  }

  Widget _buildProfileScreen() {
    return const ProfileScreen();
  }



  Widget _buildProjectsScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مخطط ذهني للطلاب',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Column(
            children: [
              // شريط التبويب
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(
                    icon: Icon(Icons.account_tree),
                    text: 'المخططات الذهنية',
                  ),
                  Tab(
                    icon: Icon(Icons.favorite),
                    text: 'المفضلة',
                  ),
                  Tab(
                    icon: Icon(Icons.school),
                    text: 'المواد الدراسية',
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuSelection,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: ListTile(
                  leading: Icon(Icons.settings),
                  title: Text('الإعدادات'),
                ),
              ),
              const PopupMenuItem(
                value: 'about',
                child: ListTile(
                  leading: Icon(Icons.info),
                  title: Text('حول التطبيق'),
                ),
              ),
            ],
          ),
        ],
      ),
      body: Consumer<MindMapProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    provider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      provider.clearError();
                      provider.loadData();
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildMindMapsTab(provider),
              _buildFavoritesTab(provider),
              _buildSubjectsTab(provider),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showCreateDialog,
        icon: const Icon(Icons.add),
        label: Text(_getFloatingActionButtonText()),
      ),
    );
  }

  Widget _buildMindMapsTab(MindMapProvider provider) {
    final mindMaps = _searchQuery.isEmpty
        ? provider.mindMaps
        : provider.searchMindMaps(_searchQuery);

    if (mindMaps.isEmpty) {
      return _buildEmptyState(
        icon: Icons.account_tree,
        title: 'لا توجد مخططات ذهنية',
        subtitle: 'ابدأ بإنشاء مخططك الذهني الأول',
        actionText: 'إنشاء مخطط',
        onAction: () => _showCreateMindMapDialog(),
      );
    }

    return RefreshIndicator(
      onRefresh: provider.loadData,
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: mindMaps.length,
        itemBuilder: (context, index) {
          final mindMap = mindMaps[index];
          return MindMapCard(
            mindMap: mindMap,
            onTap: () => _openMindMapEditor(mindMap),
            onEdit: () => _editMindMap(mindMap),
            onDelete: () => _deleteMindMap(mindMap),
          );
        },
      ),
    );
  }

  Widget _buildFavoritesTab(MindMapProvider provider) {
    final favoriteMindMaps = provider.favoriteMindMaps;

    if (favoriteMindMaps.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: 'لا توجد مخططات مفضلة',
        subtitle: 'أضف مخططاتك المهمة للمفضلة للوصول السريع إليها',
        actionText: 'استكشاف المخططات',
        onAction: () {
          _tabController.animateTo(0); // الانتقال لتبويب المخططات
        },
      );
    }

    return RefreshIndicator(
      onRefresh: provider.loadData,
      child: GridView.builder(
        padding: const EdgeInsets.all(16),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: favoriteMindMaps.length,
        itemBuilder: (context, index) {
          final mindMap = favoriteMindMaps[index];
          return MindMapCard(
            mindMap: mindMap,
            onTap: () => _openMindMapEditor(mindMap),
            onEdit: () => _editMindMap(mindMap),
            onDelete: () => _deleteMindMap(mindMap),
          );
        },
      ),
    );
  }

  Widget _buildSubjectsTab(MindMapProvider provider) {
    final subjects = _searchQuery.isEmpty
        ? provider.subjects
        : provider.searchSubjects(_searchQuery);

    if (subjects.isEmpty) {
      return _buildEmptyState(
        icon: Icons.school,
        title: 'لا توجد مواد دراسية',
        subtitle: 'أضف مادة دراسية لتنظيم مخططاتك',
        actionText: 'إضافة مادة',
        onAction: () => _showCreateSubjectDialog(),
      );
    }

    return RefreshIndicator(
      onRefresh: provider.loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: subjects.length,
        itemBuilder: (context, index) {
          final subject = subjects[index];
          return SubjectCard(
            subject: subject,
            mindMapCount: provider.getMindMapsForSubject(subject.name).length,
            onTap: () => _openSubjectDetail(subject),
            onEdit: () => _editSubject(subject),
            onDelete: () => _deleteSubject(subject),
          );
        },
      ),
    );
  }



  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required String actionText,
    required VoidCallback onAction,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 24),
          Text(
            title,
            style: Theme.of(context).textTheme.headlineSmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: onAction,
            icon: const Icon(Icons.add),
            label: Text(actionText),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث'),
        content: TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            hintText: 'ابحث في المخططات والمواد...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
                _searchController.clear();
              });
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة وترتيب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.sort_by_alpha),
              title: const Text('ترتيب أبجدي'),
              onTap: () {
                // TODO: تطبيق الترتيب الأبجدي
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Icon(Icons.access_time),
              title: const Text('الأحدث أولاً'),
              onTap: () {
                // TODO: تطبيق ترتيب بالتاريخ
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Icon(Icons.star),
              title: const Text('الأكثر استخداماً'),
              onTap: () {
                // TODO: تطبيق ترتيب بالاستخدام
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'settings':
        // TODO: فتح شاشة الإعدادات
        break;
      case 'about':
        _showAboutDialog();
        break;
    }
  }

  void _showAboutDialog() {
    showAboutDialog(
      context: context,
      applicationName: 'مخطط ذهني للطلاب',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(Icons.account_tree, size: 48),
      children: [
        const Text('تطبيق لإنشاء وإدارة المخططات الذهنية للطلاب الجامعيين'),
        const SizedBox(height: 16),
        const Text('يساعدك على تنظيم أفكارك ومعلوماتك الدراسية بطريقة بصرية وتفاعلية'),
      ],
    );
  }

  void _showCreateDialog() {
    switch (_tabController.index) {
      case 0:
        _showCreateMindMapDialog();
        break;
      case 1:
        // في تبويب المفضلة، الانتقال لتبويب المخططات
        _tabController.animateTo(0);
        break;
      case 2:
        _showCreateSubjectDialog();
        break;
    }
  }

  void _showCreateMindMapDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateMindMapDialog(),
    );
  }

  void _showCreateSubjectDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateSubjectDialog(),
    );
  }

  void _openMindMapEditor(MindMap mindMap) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MindMapEditorScreen(mindMap: mindMap),
      ),
    );
  }

  void _openSubjectDetail(Subject subject) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SubjectDetailScreen(subject: subject),
      ),
    );
  }

  void _editMindMap(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => EditMindMapDialog(mindMap: mindMap),
    );
  }

  void _deleteMindMap(MindMap mindMap) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المخطط الذهني'),
        content: Text('هل أنت متأكد من حذف "${mindMap.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<MindMapProvider>().deleteMindMap(mindMap.id);
              Navigator.pop(context);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  void _editSubject(Subject subject) {
    showDialog(
      context: context,
      builder: (context) => EditSubjectDialog(subject: subject),
    );
  }

  void _deleteSubject(Subject subject) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المادة الدراسية'),
        content: Text('هل أنت متأكد من حذف "${subject.name}"؟\nسيتم حذف جميع المخططات المرتبطة بها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              context.read<MindMapProvider>().deleteSubject(subject.id);
              Navigator.pop(context);
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  String _getFloatingActionButtonText() {
    switch (_tabController.index) {
      case 0:
        return 'مخطط جديد';
      case 1:
        return 'استكشاف المخططات';
      case 2:
        return 'مادة جديدة';
      default:
        return 'إضافة';
    }
  }
}

