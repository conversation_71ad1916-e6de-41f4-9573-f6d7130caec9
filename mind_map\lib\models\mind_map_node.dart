import 'dart:ui';
import 'package:uuid/uuid.dart';

// أشكال العقد
enum NodeShape {
  rectangle,
  circle,
  oval,
  diamond,
  hexagon,
}

class MindMapNode {
  final String id;
  String title;
  String description;
  Offset position;
  Color color;
  double width;
  double height;
  NodeShape shape;
  double fontSize;
  bool isRoot;
  List<String> childrenIds;
  String? parentId;
  bool isExpanded;
  DateTime createdAt;
  DateTime updatedAt;

  MindMapNode({
    String? id,
    required this.title,
    this.description = '',
    required this.position,
    this.color = const Color(0xFF2196F3),
    this.width = 140.0,
    this.height = 80.0,
    this.shape = NodeShape.rectangle,
    this.fontSize = 14.0,
    this.isRoot = false,
    List<String>? childrenIds,
    this.parentId,
    this.isExpanded = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       childrenIds = childrenIds ?? [],
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  // إنشاء نسخة من العقدة مع تعديل بعض الخصائص
  MindMapNode copyWith({
    String? title,
    String? description,
    Offset? position,
    Color? color,
    double? width,
    double? height,
    NodeShape? shape,
    double? fontSize,
    bool? isRoot,
    List<String>? childrenIds,
    String? parentId,
    bool? isExpanded,
  }) {
    return MindMapNode(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      position: position ?? this.position,
      color: color ?? this.color,
      width: width ?? this.width,
      height: height ?? this.height,
      shape: shape ?? this.shape,
      fontSize: fontSize ?? this.fontSize,
      isRoot: isRoot ?? this.isRoot,
      childrenIds: childrenIds ?? List.from(this.childrenIds),
      parentId: parentId ?? this.parentId,
      isExpanded: isExpanded ?? this.isExpanded,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'position': {'dx': position.dx, 'dy': position.dy},
      'color': color.value.toRadixString(16),
      'width': width,
      'height': height,
      'shape': shape.name,
      'fontSize': fontSize,
      'isRoot': isRoot,
      'childrenIds': childrenIds,
      'parentId': parentId,
      'isExpanded': isExpanded,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // إنشاء من JSON
  factory MindMapNode.fromJson(Map<String, dynamic> json) {
    return MindMapNode(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      position: Offset(
        json['position']['dx'].toDouble(),
        json['position']['dy'].toDouble(),
      ),
      color: Color(int.parse(json['color'] as String, radix: 16)),
      width: json['width'].toDouble(),
      height: json['height'].toDouble(),
      shape: NodeShape.values.firstWhere(
        (shape) => shape.name == json['shape'],
        orElse: () => NodeShape.rectangle,
      ),
      fontSize: json['fontSize']?.toDouble() ?? 14.0,
      isRoot: json['isRoot'] ?? false,
      childrenIds: List<String>.from(json['childrenIds'] ?? []),
      parentId: json['parentId'],
      isExpanded: json['isExpanded'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  // إضافة عقدة فرعية
  void addChild(String childId) {
    if (!childrenIds.contains(childId)) {
      childrenIds.add(childId);
      updatedAt = DateTime.now();
    }
  }

  // إزالة عقدة فرعية
  void removeChild(String childId) {
    childrenIds.remove(childId);
    updatedAt = DateTime.now();
  }

  // تحديث الموقع
  void updatePosition(Offset newPosition) {
    position = newPosition;
    updatedAt = DateTime.now();
  }

  // تحديث النص
  void updateTitle(String newTitle) {
    title = newTitle;
    updatedAt = DateTime.now();
  }

  // تحديث الوصف
  void updateDescription(String newDescription) {
    description = newDescription;
    updatedAt = DateTime.now();
  }

  // تحديث اللون
  void updateColor(Color newColor) {
    color = newColor;
    updatedAt = DateTime.now();
  }

  // تحديث الحجم
  void updateSize(double newWidth, double newHeight) {
    width = newWidth;
    height = newHeight;
    updatedAt = DateTime.now();
  }

  // تبديل حالة التوسع
  void toggleExpanded() {
    isExpanded = !isExpanded;
    updatedAt = DateTime.now();
  }

  @override
  String toString() {
    return 'MindMapNode(id: $id, title: $title, position: $position)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MindMapNode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
