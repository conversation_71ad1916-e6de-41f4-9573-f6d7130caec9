import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/notification.dart';
import '../providers/auth_provider.dart';
import '../screens/posts_screen.dart';
import '../screens/users_search_screen.dart';
import '../screens/user_profile_screen.dart';

class NotificationItem extends StatelessWidget {
  final AppNotification notification;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;

  const NotificationItem({
    super.key,
    required this.notification,
    this.onTap,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      onDismissed: (_) => onDismiss?.call(),
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20),
        color: Colors.red,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
          size: 28,
        ),
      ),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        elevation: notification.isRead ? 1 : 3,
        color: notification.isRead ? null : Theme.of(context).primaryColor.withValues(alpha: 0.05),
        child: ListTile(
          onTap: () {
            onTap?.call();
            _handleNotificationTap(context);
          },
          leading: _buildLeadingIcon(context),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.message,
                style: TextStyle(
                  color: notification.isRead ? Colors.grey[600] : Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _formatTime(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          trailing: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!notification.isRead)
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
              const SizedBox(height: 8),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    IconData iconData;
    Color iconColor;
    String? reactionEmoji;

    switch (notification.type) {
      case NotificationType.follow:
        iconData = Icons.person_add;
        iconColor = Colors.blue;
        break;
      case NotificationType.newPost:
        iconData = Icons.post_add;
        iconColor = Colors.green;
        break;
      case NotificationType.like:
        iconData = Icons.favorite;
        iconColor = Colors.red;
        break;
      case NotificationType.comment:
        iconData = Icons.comment;
        iconColor = Colors.orange;
        break;
      case NotificationType.reply:
        iconData = Icons.reply;
        iconColor = Colors.purple;
        break;
      case NotificationType.reaction:
        // استخراج الإيموجي من العنوان
        reactionEmoji = _extractEmojiFromTitle();
        iconData = Icons.emoji_emotions;
        iconColor = Colors.amber;
        break;
      case NotificationType.reportResolved:
        iconData = Icons.check_circle;
        iconColor = Colors.green;
        break;
    }

    // إذا كان هناك إيموجي رد فعل، اعرضه بدلاً من الأيقونة
    if (reactionEmoji != null) {
      return CircleAvatar(
        backgroundColor: iconColor.withValues(alpha: 0.1),
        child: Text(
          reactionEmoji,
          style: const TextStyle(fontSize: 20),
        ),
      );
    }

    // عرض الأيقونة العادية
    return CircleAvatar(
      backgroundColor: iconColor.withValues(alpha: 0.1),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String? _extractEmojiFromTitle() {
    // استخراج الإيموجي من العنوان
    final title = notification.title;
    final emojiRegex = RegExp(r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]', unicode: true);
    final match = emojiRegex.firstMatch(title);
    return match?.group(0);
  }

  Widget _buildLeadingIcon(BuildContext context) {
    return Stack(
      children: [
        _buildNotificationIcon(),
        // صورة المستخدم إذا كانت متوفرة
        if (notification.fromUserAvatar != null && notification.fromUserAvatar!.isNotEmpty)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
                image: DecorationImage(
                  image: NetworkImage(notification.fromUserAvatar!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
      ],
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks أسبوع';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months شهر';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'منذ $years سنة';
    }
  }

  void _handleNotificationTap(BuildContext context) {
    switch (notification.type) {
      case NotificationType.follow:
        _navigateToUserProfile(context, notification.fromUserId);
        break;
      case NotificationType.newPost:
      case NotificationType.like:
      case NotificationType.reaction:
        _navigateToPost(context, notification.relatedPostId);
        break;
      case NotificationType.comment:
      case NotificationType.reply:
        _navigateToComment(context, notification.relatedPostId, notification.relatedCommentId);
        break;
      case NotificationType.reportResolved:
        _showReportResolvedDialog(context);
        break;
    }
  }

  void _navigateToUserProfile(BuildContext context, String? userId) async {
    if (userId == null) return;

    try {
      // البحث عن المستخدم في AuthProvider
      final authProvider = context.read<AuthProvider>();
      final users = await authProvider.searchUsers(limit: 100);

      final user = users.where((u) => u.uid == userId).firstOrNull;

      if (context.mounted && user != null) {
        // التنقل إلى ملف المستخدم الشخصي
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => UserProfileScreen(user: user),
          ),
        );
      } else if (context.mounted) {
        // إذا لم يتم العثور على المستخدم، اذهب لشاشة البحث
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UsersSearchScreen(),
          ),
        );
      }
    } catch (error) {
      if (context.mounted) {
        // في حالة الخطأ، اذهب لشاشة البحث
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UsersSearchScreen(),
          ),
        );
      }
    }
  }

  void _navigateToPost(BuildContext context, String? postId) {
    if (postId == null) return;

    // التنقل إلى شاشة المنشورات
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PostsScreen(),
      ),
    );
  }

  void _navigateToComment(BuildContext context, String? postId, String? commentId) {
    if (postId == null || commentId == null) return;

    // التنقل إلى الشاشة الرئيسية مع التركيز على التعليق
    Navigator.popUntil(context, (route) => route.isFirst);

    // التنقل إلى تبويب المنشورات
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PostsScreen(
          highlightPostId: postId,
          highlightCommentId: commentId,
        ),
      ),
    );
  }

  // عرض حوار تفاصيل البلاغ المحلول
  void _showReportResolvedDialog(BuildContext context) {
    final additionalData = notification.additionalData ?? {};
    final reportReason = additionalData['reportReason']?.toString() ?? 'غير محدد';
    final postTitle = additionalData['postTitle']?.toString() ?? 'المنشور';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('تم مراجعة بلاغك'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات البلاغ
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تفاصيل البلاغ:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow('المنشور المبلغ عنه:', postTitle),
                    _buildDetailRow('سبب البلاغ:', _getReasonDisplayName(reportReason)),
                    _buildDetailRow('تاريخ البلاغ:', _formatNotificationDate(notification.createdAt)),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // رسالة الإدارة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.admin_panel_settings,
                             color: Colors.green.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'رد الإدارة:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'تم مراجعة بلاغك من قبل فريق الإدارة واتخاذ الإجراء المناسب. نشكرك على مساهمتك في الحفاظ على جودة المحتوى في التطبيق.',
                      style: TextStyle(fontSize: 14),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade100,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle_outline,
                               color: Colors.green.shade700, size: 16),
                          const SizedBox(width: 6),
                          Text(
                            'تم حل البلاغ',
                            style: TextStyle(
                              color: Colors.green.shade700,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange.shade700, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'إذا كان لديك أي استفسارات أخرى، يمكنك التواصل مع فريق الدعم.',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToPost(context, notification.relatedPostId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('عرض المنشور'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  String _getReasonDisplayName(String reason) {
    switch (reason) {
      case 'spam':
        return 'محتوى مزعج';
      case 'inappropriate':
        return 'محتوى غير مناسب';
      case 'harassment':
        return 'تحرش أو إساءة';
      case 'falseInfo':
        return 'معلومات خاطئة';
      case 'copyright':
        return 'انتهاك حقوق الطبع';
      case 'violence':
        return 'عنف أو تهديد';
      case 'other':
        return 'أخرى';
      default:
        return reason;
    }
  }

  String _formatNotificationDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم مضى';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة مضت';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة مضت';
    } else {
      return 'الآن';
    }
  }
}
