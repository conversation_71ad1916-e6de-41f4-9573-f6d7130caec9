import 'package:flutter/material.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/post.dart';

class AdminPostsManagementScreen extends StatefulWidget {
  const AdminPostsManagementScreen({super.key});

  @override
  State<AdminPostsManagementScreen> createState() => _AdminPostsManagementScreenState();
}

class _AdminPostsManagementScreenState extends State<AdminPostsManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Post> _posts = [];
  List<Post> _filteredPosts = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, recent, popular, reported
  String _selectedSort = 'newest'; // newest, oldest, most_liked, most_commented

  @override
  void initState() {
    super.initState();
    _loadPosts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final database = FirebaseDatabase.instance;
      final postsRef = database.ref('posts');
      final snapshot = await postsRef.get();

      if (snapshot.exists && snapshot.value != null) {
        final postsData = Map<String, dynamic>.from(snapshot.value as Map);
        final posts = <Post>[];

        for (final entry in postsData.entries) {
          try {
            if (entry.value == null) {
              debugPrint('تخطي منشور فارغ: ${entry.key}');
              continue;
            }

            final postData = _convertToStringDynamicMap(entry.value);
            final post = Post.fromJson(postData);
            posts.add(post);
            debugPrint('✅ تم تحميل منشور: ${post.mindMapTitle}');
          } catch (e) {
            debugPrint('❌ خطأ في تحميل منشور ${entry.key}: $e');
            debugPrint('📝 بيانات المنشور: ${entry.value}');
          }
        }

        setState(() {
          _posts = posts;
          _applyFiltersAndSort();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنشورات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      final Map<String, dynamic> result = {};
      data.forEach((key, value) {
        if (key != null) {
          result[key.toString()] = _convertValue(value);
        }
      });
      return result;
    } else {
      throw Exception('البيانات ليست من نوع Map: ${data.runtimeType}');
    }
  }

  dynamic _convertValue(dynamic value) {
    if (value is Map && value is! Map<String, dynamic>) {
      final Map<String, dynamic> result = {};
      value.forEach((key, val) {
        if (key != null) {
          result[key.toString()] = _convertValue(val);
        }
      });
      return result;
    } else if (value is List) {
      return value.map((item) => _convertValue(item)).toList();
    } else {
      return value;
    }
  }

  void _applyFiltersAndSort() async {
    List<Post> filtered = List.from(_posts);

    // تطبيق البحث
    final searchQuery = _searchController.text.toLowerCase();
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((post) {
        return post.mindMapTitle.toLowerCase().contains(searchQuery) ||
               post.authorName.toLowerCase().contains(searchQuery) ||
               post.mindMapSubject.toLowerCase().contains(searchQuery) ||
               post.description.toLowerCase().contains(searchQuery);
      }).toList();
    }

    // تطبيق الفلاتر
    switch (_selectedFilter) {
      case 'recent':
        final now = DateTime.now();
        final weekAgo = now.subtract(const Duration(days: 7));
        filtered = filtered.where((post) => post.createdAt.isAfter(weekAgo)).toList();
        break;
      case 'popular':
        filtered = filtered.where((post) =>
          (post.likes.length + post.reactions.totalCount + post.comments.length) >= 5
        ).toList();
        break;
      case 'reported':
        filtered = await _filterReportedPosts(filtered);
        break;
    }

    // تطبيق الترتيب
    switch (_selectedSort) {
      case 'newest':
        filtered.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'oldest':
        filtered.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case 'most_liked':
        filtered.sort((a, b) =>
          (b.likes.length + b.reactions.totalCount).compareTo(a.likes.length + a.reactions.totalCount)
        );
        break;
      case 'most_commented':
        filtered.sort((a, b) => b.comments.length.compareTo(a.comments.length));
        break;
    }

    setState(() {
      _filteredPosts = filtered;
    });
  }

  // فلترة المنشورات المبلغ عنها
  Future<List<Post>> _filterReportedPosts(List<Post> posts) async {
    try {
      final database = FirebaseDatabase.instance;
      final reportsRef = database.ref('post_reports');
      final snapshot = await reportsRef.get();

      if (!snapshot.exists || snapshot.value == null) {
        return [];
      }

      final reportsData = Map<String, dynamic>.from(snapshot.value as Map);
      final reportedPostIds = <String>{};

      // جمع معرفات المنشورات المبلغ عنها (غير المحلولة)
      for (final entry in reportsData.entries) {
        try {
          final reportData = _convertToStringDynamicMap(entry.value);
          final isResolved = reportData['isResolved'] ?? false;

          if (!isResolved) {
            final postId = reportData['postId'];
            if (postId != null) {
              reportedPostIds.add(postId.toString());
            }
          }
        } catch (e) {
          debugPrint('خطأ في معالجة بلاغ: $e');
        }
      }

      // فلترة المنشورات المبلغ عنها
      return posts.where((post) => reportedPostIds.contains(post.id)).toList();
    } catch (e) {
      debugPrint('خطأ في تحميل المنشورات المبلغ عنها: $e');
      return [];
    }
  }

  // فحص ما إذا كان المنشور مبلغ عنه
  Future<bool> _isPostReported(String postId) async {
    try {
      final database = FirebaseDatabase.instance;
      final reportsRef = database.ref('post_reports');
      final snapshot = await reportsRef.orderByChild('postId').equalTo(postId).get();

      if (!snapshot.exists || snapshot.value == null) {
        return false;
      }

      final reportsData = Map<String, dynamic>.from(snapshot.value as Map);

      // فحص وجود بلاغات غير محلولة
      for (final entry in reportsData.entries) {
        try {
          final reportData = _convertToStringDynamicMap(entry.value);
          final isResolved = reportData['isResolved'] ?? false;

          if (!isResolved) {
            return true;
          }
        } catch (e) {
          debugPrint('خطأ في فحص بلاغ: $e');
        }
      }

      return false;
    } catch (e) {
      debugPrint('خطأ في فحص البلاغات: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إدارة المنشورات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.orange.shade600,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadPosts,
            icon: const Icon(Icons.refresh_rounded),
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete_all') {
                _showDeleteAllConfirmation();
              } else if (value == 'export') {
                _exportPosts();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('تصدير البيانات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete_all',
                child: Row(
                  children: [
                    Icon(Icons.delete_sweep, color: Colors.red),
                    SizedBox(width: 8),
                    Text(
                      'حذف جميع المنشورات',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والفلاتر
          _buildSearchAndFilters(),

          // إحصائيات سريعة
          _buildQuickStats(),

          // قائمة المنشورات
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredPosts.isEmpty
                    ? _buildEmptyState()
                    : _buildPostsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في المنشورات...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        _searchController.clear();
                        _applyFiltersAndSort();
                      },
                      icon: const Icon(Icons.clear),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.orange.shade600),
              ),
            ),
            onChanged: (value) => _applyFiltersAndSort(),
          ),
          const SizedBox(height: 12),

          // الفلاتر والترتيب
          Row(
            children: [
              // فلتر النوع
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: InputDecoration(
                    labelText: 'الفلتر',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('جميع المنشورات')),
                    DropdownMenuItem(value: 'recent', child: Text('الأسبوع الماضي')),
                    DropdownMenuItem(value: 'popular', child: Text('الأكثر تفاعلاً')),
                    DropdownMenuItem(value: 'reported', child: Text('المبلغ عنها')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                    _applyFiltersAndSort();
                  },
                ),
              ),
              const SizedBox(width: 12),

              // ترتيب
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedSort,
                  decoration: InputDecoration(
                    labelText: 'الترتيب',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'newest', child: Text('الأحدث')),
                    DropdownMenuItem(value: 'oldest', child: Text('الأقدم')),
                    DropdownMenuItem(value: 'most_liked', child: Text('الأكثر إعجاباً')),
                    DropdownMenuItem(value: 'most_commented', child: Text('الأكثر تعليقاً')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedSort = value!;
                    });
                    _applyFiltersAndSort();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final totalPosts = _posts.length;
    final totalLikes = _posts.fold<int>(0, (sum, post) => sum + post.likes.length + post.reactions.totalCount);
    final totalComments = _posts.fold<int>(0, (sum, post) => sum + post.comments.length);
    final recentPosts = _posts.where((post) =>
      post.createdAt.isAfter(DateTime.now().subtract(const Duration(days: 7)))
    ).length;

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'إجمالي المنشورات',
              totalPosts.toString(),
              Icons.post_add,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'هذا الأسبوع',
              recentPosts.toString(),
              Icons.schedule,
              Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'إجمالي الإعجابات',
              totalLikes.toString(),
              Icons.favorite,
              Colors.red,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'إجمالي التعليقات',
              totalComments.toString(),
              Icons.comment,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.post_add_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isNotEmpty
                ? 'لا توجد منشورات تطابق البحث'
                : 'لا توجد منشورات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchController.text.isNotEmpty
                ? 'جرب البحث بكلمات مختلفة'
                : 'لم يتم إنشاء أي منشورات بعد',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
          if (_searchController.text.isNotEmpty) ...[
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _searchController.clear();
                _applyFiltersAndSort();
              },
              child: const Text('مسح البحث'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPostsList() {
    return RefreshIndicator(
      onRefresh: _loadPosts,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredPosts.length,
        itemBuilder: (context, index) {
          final post = _filteredPosts[index];
          return _buildPostCard(post);
        },
      ),
    );
  }

  Widget _buildPostCard(Post post) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // مؤشر المنشور المبلغ عنه
            FutureBuilder<bool>(
              future: _isPostReported(post.id),
              builder: (context, snapshot) {
                if (snapshot.data == true) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.report, size: 16, color: Colors.red.shade700),
                        const SizedBox(width: 4),
                        Text(
                          'مبلغ عنه',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            // معلومات المؤلف والتاريخ
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.orange.shade100,
                  child: Text(
                    post.authorName.isNotEmpty ? post.authorName[0] : 'U',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.authorName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        post.authorUniversity,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  _formatDate(post.createdAt),
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 12,
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handlePostAction(value, post),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'view',
                      child: Row(
                        children: [
                          Icon(Icons.visibility, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('عرض التفاصيل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text(
                            'حذف',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),

            // عنوان المخطط الذهني
            Text(
              post.mindMapTitle,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),

            // المادة
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Text(
                post.mindMapSubject,
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // الوصف
            if (post.description.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                post.description,
                style: TextStyle(
                  color: Colors.grey.shade700,
                  fontSize: 14,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],

            const SizedBox(height: 12),

            // إحصائيات التفاعل
            Row(
              children: [
                _buildInteractionStat(
                  Icons.favorite,
                  post.likes.length + post.reactions.totalCount,
                  Colors.red,
                ),
                const SizedBox(width: 16),
                _buildInteractionStat(
                  Icons.comment,
                  post.comments.length,
                  Colors.blue,
                ),
                const Spacer(),
                Text(
                  'ID: ${post.id.substring(0, 8)}...',
                  style: TextStyle(
                    color: Colors.grey.shade400,
                    fontSize: 10,
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractionStat(IconData icon, int count, Color color) {
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  void _handlePostAction(String action, Post post) {
    switch (action) {
      case 'view':
        _showPostDetails(post);
        break;
      case 'edit':
        _editPost(post);
        break;
      case 'delete':
        _deletePost(post);
        break;
    }
  }

  void _showPostDetails(Post post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(post.mindMapTitle),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('المؤلف', post.authorName),
              _buildDetailRow('الجامعة', post.authorUniversity),
              _buildDetailRow('المادة', post.mindMapSubject),
              _buildDetailRow('تاريخ النشر', '${post.createdAt.day}/${post.createdAt.month}/${post.createdAt.year}'),
              _buildDetailRow('الإعجابات', '${post.likes.length + post.reactions.totalCount}'),
              _buildDetailRow('التعليقات', '${post.comments.length}'),
              if (post.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'الوصف:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(post.description),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _editPost(Post post) {
    final descriptionController = TextEditingController(text: post.description);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل المنشور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('تعديل وصف المنشور: ${post.mindMapTitle}'),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final database = FirebaseDatabase.instance;
                await database.ref('posts/${post.id}').update({
                  'description': descriptionController.text,
                  'updatedAt': DateTime.now().toIso8601String(),
                });

                if (mounted) {
                  Navigator.pop(context);
                  _loadPosts();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث المنشور بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في تحديث المنشور: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _deletePost(Post post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('تأكيد الحذف'),
          ],
        ),
        content: Text('هل أنت متأكد من حذف منشور "${post.mindMapTitle}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final database = FirebaseDatabase.instance;
                await database.ref('posts/${post.id}').remove();

                if (mounted) {
                  Navigator.pop(context);
                  _loadPosts();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المنشور بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في حذف المنشور: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showDeleteAllConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('تحذير خطير'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من حذف جميع المنشورات؟',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('سيتم حذف:'),
            Text('• جميع المنشورات'),
            Text('• جميع التعليقات'),
            Text('• جميع الإعجابات وردود الفعل'),
            SizedBox(height: 12),
            Text(
              'هذا الإجراء لا يمكن التراجع عنه!',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteAllPosts();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف الكل', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAllPosts() async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري حذف جميع المنشورات...'),
            ],
          ),
        ),
      );

      final database = FirebaseDatabase.instance;
      await database.ref('posts').remove();

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        _loadPosts();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف جميع المنشورات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف المنشورات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportPosts() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التصدير قيد التطوير'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}