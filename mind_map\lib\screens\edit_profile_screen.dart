import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../models/user_model.dart';

class EditProfileScreen extends StatefulWidget {
  final UserModel userModel;

  const EditProfileScreen({
    super.key,
    required this.userModel,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _universityController;
  late TextEditingController _majorController;
  late TextEditingController _bioController;
  DateTime? _selectedBirthDate;
  int? _calculatedAge;

  @override
  void initState() {
    super.initState();
    _firstNameController = TextEditingController(text: widget.userModel.firstName);
    _lastNameController = TextEditingController(text: widget.userModel.lastName);
    _universityController = TextEditingController(text: widget.userModel.university);
    _majorController = TextEditingController(text: widget.userModel.major);
    _bioController = TextEditingController(text: widget.userModel.bio);
    _selectedBirthDate = widget.userModel.birthDate;
    _calculateAge();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _universityController.dispose();
    _majorController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  // حساب العمر من تاريخ الميلاد
  void _calculateAge() {
    if (_selectedBirthDate != null) {
      final now = DateTime.now();
      int age = now.year - _selectedBirthDate!.year;
      
      if (now.month < _selectedBirthDate!.month || 
          (now.month == _selectedBirthDate!.month && now.day < _selectedBirthDate!.day)) {
        age--;
      }
      
      setState(() {
        _calculatedAge = age;
      });
    }
  }

  // اختيار تاريخ الميلاد
  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedBirthDate ?? DateTime.now().subtract(const Duration(days: 365 * 20)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 100)),
      lastDate: DateTime.now().subtract(const Duration(days: 365 * 16)),
      helpText: 'اختر تاريخ الميلاد',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
      _calculateAge();
    }
  }

  // حفظ التعديلات
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final success = await authProvider.updateUserProfile(
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      university: _universityController.text.trim(),
      major: _majorController.text.trim(),
      bio: _bioController.text.trim(),
      birthDate: _selectedBirthDate,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديث الملف الشخصي بنجاح')),
      );
      Navigator.of(context).pop();
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('فشل في تحديث الملف الشخصي')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل الملف الشخصي'),
        actions: [
          Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              return authProvider.isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : TextButton(
                      onPressed: _saveChanges,
                      child: const Text(
                        'حفظ',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    );
            },
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // الاسم الأول
              TextFormField(
                controller: _firstNameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم الأول',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال الاسم الأول';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // اللقب
              TextFormField(
                controller: _lastNameController,
                decoration: const InputDecoration(
                  labelText: 'اللقب',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person_outline),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اللقب';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // الجامعة
              TextFormField(
                controller: _universityController,
                decoration: const InputDecoration(
                  labelText: 'الجامعة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.school),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم الجامعة';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // التخصص
              TextFormField(
                controller: _majorController,
                decoration: const InputDecoration(
                  labelText: 'التخصص',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.book),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال التخصص';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // النبذة الشخصية
              TextFormField(
                controller: _bioController,
                decoration: const InputDecoration(
                  labelText: 'النبذة الشخصية (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.info_outline),
                  hintText: 'اكتب نبذة مختصرة عن نفسك...',
                ),
                maxLines: 3,
                maxLength: 200,
              ),
              
              const SizedBox(height: 16),
              
              // تاريخ الميلاد
              InkWell(
                onTap: _selectBirthDate,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'تاريخ الميلاد',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                            Text(
                              _selectedBirthDate != null
                                  ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                                  : 'اختر تاريخ الميلاد',
                              style: const TextStyle(fontSize: 16),
                            ),
                          ],
                        ),
                      ),
                      if (_calculatedAge != null)
                        Text(
                          'العمر: $_calculatedAge سنة',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
