import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/mind_map_provider.dart';
import '../models/user_model.dart';
import 'edit_profile_screen.dart';
import 'admin_statistics_screen.dart';
import '../widgets/logout_loading_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث آخر نشاط وإعادة تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.updateLastActive();

      // إعادة تحميل بيانات المستخدم إذا لم تكن موجودة
      if (authProvider.user != null && authProvider.userModel == null) {
        print('🔄 إعادة تحميل بيانات المستخدم في البروفايل...');
        authProvider.reloadUserData();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.user;
          final userModel = authProvider.userModel;
          final isAnonymous = user?.isAnonymous ?? false;

          // Debug information
          print('🔍 Profile Screen Debug:');
          print('   User: ${user?.uid}');
          print('   UserModel: ${userModel?.email}');
          print('   IsAnonymous: $isAnonymous');
          print('   IsLoading: ${authProvider.isLoading}');

          return CustomScrollView(
            slivers: [
              // AppBar مخصص مع تدرج لوني
              SliverAppBar(
                expandedHeight: 180,
                floating: false,
                pinned: true,
                elevation: 0,
                title: const Text(
                  'الملف الشخصي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                centerTitle: true,
                backgroundColor: Colors.blue.shade700,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.blue.shade500,
                          Colors.blue.shade700,
                          Colors.blue.shade900,
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.shade200.withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: SafeArea(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          const SizedBox(height: 60), // مساحة للعنوان الثابت
                          // صورة المستخدم
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 3,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 15,
                                  offset: const Offset(0, 8),
                                ),
                                BoxShadow(
                                  color: Colors.white.withValues(alpha: 0.1),
                                  blurRadius: 5,
                                  offset: const Offset(0, -2),
                                ),
                              ],
                            ),
                            child: CircleAvatar(
                              radius: 45,
                              backgroundColor: Colors.white,
                              child: Icon(
                                isAnonymous ? Icons.person_outline : Icons.person,
                                size: 50,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // محتوى الصفحة
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      // بطاقة المعلومات الأساسية
                      _buildMainInfoCard(isAnonymous, userModel, user),

                      const SizedBox(height: 16),

                      // بطاقة المعلومات الأكاديمية
                      if (!isAnonymous && userModel != null) ...[
                        _buildAcademicInfoCard(userModel),
                        const SizedBox(height: 16),
                        _buildPersonalInfoCard(userModel),
                        const SizedBox(height: 16),
                        _buildProjectsStatsCard(context),
                        const SizedBox(height: 16),
                        _buildFollowStatsCard(userModel),
                        const SizedBox(height: 16),
                        _buildActionButtons(context, userModel),
                      ],

                      // بطاقة خيارات الضيف
                      if (isAnonymous) ...[
                        _buildGuestOptionsCard(context),
                      ],

                      const SizedBox(height: 16),

                      // زر تسجيل الخروج
                      _buildSignOutButton(context, authProvider),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }


  }

  // بطاقة المعلومات الأساسية
  Widget _buildMainInfoCard(bool isAnonymous, userModel, user) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.blue.shade50,
            ],
          ),
        ),
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            // اسم المستخدم
            Text(
              isAnonymous
                  ? 'مستخدم ضيف'
                  : (userModel?.fullName ?? user?.email ?? 'مستخدم'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),

            // البريد الإلكتروني
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.email, size: 16, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Text(
                    isAnonymous
                        ? 'تم تسجيل الدخول كضيف'
                        : (user?.email ?? 'مستخدم مسجل'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // حالة النشاط
            if (!isAnonymous && userModel != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: userModel.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: userModel.isActive ? Colors.green.shade200 : Colors.grey.shade200,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: userModel.isActive ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      userModel.isActive
                          ? 'نشط الآن'
                          : 'غير نشط منذ ${userModel.inactiveTime}',
                      style: TextStyle(
                        fontSize: 14,
                        color: userModel.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // النبذة الشخصية
            if (!isAnonymous && userModel != null && userModel.bio.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, size: 20, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'النبذة الشخصية',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      userModel.bio,
                      style: const TextStyle(fontSize: 14, height: 1.5),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الأكاديمية
  Widget _buildAcademicInfoCard(userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الأكاديمية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.school, 'الجامعة', userModel.university),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.book, 'التخصص', userModel.major),
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الشخصية
  Widget _buildPersonalInfoCard(userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الشخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.cake, 'العمر', '${userModel.age} سنة'),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الميلاد',
              '${userModel.birthDate.day}/${userModel.birthDate.month}/${userModel.birthDate.year}'
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.person_add,
              'تاريخ إنشاء الحساب',
              '${userModel.createdAt.day}/${userModel.createdAt.month}/${userModel.createdAt.year}'
            ),
          ],
        ),
      ),
    );
  }

  // أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context, userModel) {
    return Column(
      children: [
        // زر تعديل الملف الشخصي
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => EditProfileScreen(userModel: userModel),
                ),
              );
            },
            icon: const Icon(Icons.edit),
            label: const Text('تعديل الملف الشخصي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // زر الإحصائيات الإدارية (للمدراء فقط)
        if (userModel.isAdmin) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AdminStatisticsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.analytics_rounded),
              label: const Text('إحصائيات التطبيق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
      ],
    );
  }

  // بطاقة خيارات الضيف
  Widget _buildGuestOptionsCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade50,
              Colors.orange.shade100,
            ],
          ),
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.orange.shade700,
            ),
            const SizedBox(height: 16),
            const Text(
              'أنت تستخدم التطبيق كضيف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سجل حساب جديد أو ادخل بحسابك لحفظ بياناتك في السحابة والاستفادة من جميع الميزات',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _signOutAndGoToAuth(context, Provider.of<AuthProvider>(context, listen: false)),
                    icon: const Icon(Icons.person_add),
                    label: const Text('إنشاء حساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _signOutAndGoToAuth(context, Provider.of<AuthProvider>(context, listen: false)),
                    icon: const Icon(Icons.login),
                    label: const Text('تسجيل دخول'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // زر تسجيل الخروج
  Widget _buildSignOutButton(BuildContext context, AuthProvider authProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: (authProvider.isLoading || authProvider.isLoggingOut)
            ? null
            : () => _signOutAndGoToAuth(context, authProvider),
        icon: (authProvider.isLoading || authProvider.isLoggingOut)
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            )
          : const Icon(Icons.logout),
        label: Text(
          (authProvider.isLoading || authProvider.isLoggingOut)
              ? 'جاري تسجيل الخروج...'
              : 'تسجيل الخروج'
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  // دالة مساعدة لعرض صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.blue.shade700),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  // بطاقة إحصائيات المتابعة
  Widget _buildFollowStatsCard(UserModel userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.shade50,
              Colors.pink.shade50,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.people, color: Colors.purple.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'إحصائيات المتابعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildFollowStatItem(
                    icon: Icons.people,
                    count: userModel.followersCount,
                    label: 'متابع',
                    color: Colors.blue,
                  ),
                ),
                Container(
                  width: 1,
                  height: 50,
                  color: Colors.grey.shade300,
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                ),
                Expanded(
                  child: _buildFollowStatItem(
                    icon: Icons.person_add,
                    count: userModel.followingCount,
                    label: 'متابَع',
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  // بطاقة إحصائيات المشاريع
  Widget _buildProjectsStatsCard(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, mindMapProvider, child) {
        final totalMindMaps = mindMapProvider.mindMaps.length;
        final totalSubjects = mindMapProvider.subjects.length;
        final favoriteMindMaps = mindMapProvider.mindMaps.where((m) => m.isFavorite).length;
        final recentMindMaps = mindMapProvider.mindMaps
            .where((m) => DateTime.now().difference(m.updatedAt).inDays <= 7)
            .length;

        return Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.blue.shade50,
                ],
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        color: Colors.purple.shade700,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'إحصائيات المشاريع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // شبكة الإحصائيات
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 1.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    _buildStatItem(
                      icon: Icons.psychology_outlined,
                      title: 'الخرائط الذهنية',
                      value: totalMindMaps.toString(),
                      color: Colors.blue,
                    ),
                    _buildStatItem(
                      icon: Icons.school_outlined,
                      title: 'المواد الدراسية',
                      value: totalSubjects.toString(),
                      color: Colors.green,
                    ),
                    _buildStatItem(
                      icon: Icons.favorite_outline,
                      title: 'المفضلة',
                      value: favoriteMindMaps.toString(),
                      color: Colors.red,
                    ),
                    _buildStatItem(
                      icon: Icons.schedule_outlined,
                      title: 'الحديثة',
                      value: recentMindMaps.toString(),
                      color: Colors.orange,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عنصر إحصائية واحد
  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // دالة تسجيل الخروج والانتقال لشاشة المصادقة
  Future<void> _signOutAndGoToAuth(BuildContext context, AuthProvider authProvider) async {
    // إظهار حوار تأكيد
    final shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (shouldSignOut == true && context.mounted) {
      // عرض شاشة التحميل
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => LogoutLoadingScreen(
            title: 'جاري تسجيل الخروج...',
            subtitle: 'شكراً لاستخدام التطبيق',
            duration: const Duration(seconds: 3),
            gradientColors: [
              Colors.blue.shade500,
              Colors.blue.shade700,
              Colors.blue.shade900,
            ],
            onComplete: () async {
              // تسجيل الخروج الفعلي
              await authProvider.signOut();
              if (context.mounted) {
                Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
              }
            },
          ),
        ),
      );
    }
  }

