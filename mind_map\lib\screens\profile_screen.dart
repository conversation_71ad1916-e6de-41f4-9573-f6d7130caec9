import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/mind_map_provider.dart';
import 'edit_profile_screen.dart';
import 'admin_statistics_screen.dart';
import '../widgets/logout_loading_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // تحديث آخر نشاط وإعادة تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.updateLastActive();

      // إعادة تحميل بيانات المستخدم إذا لم تكن موجودة
      if (authProvider.user != null && authProvider.userModel == null) {
        print('🔄 إعادة تحميل بيانات المستخدم في البروفايل...');
        authProvider.reloadUserData();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return Consumer<AuthProvider>(
            builder: (context, authProvider, child) {
              final user = authProvider.user;
              final userModel = authProvider.userModel;
              final isAnonymous = user?.isAnonymous ?? false;

              // Debug information
              print('🔍 Profile Screen Debug:');
              print('   User: ${user?.uid}');
              print('   UserModel: ${userModel?.email}');
              print('   IsAnonymous: $isAnonymous');
              print('   IsLoading: ${authProvider.isLoading}');

              return _buildResponsiveLayout(
                constraints: constraints,
                isAnonymous: isAnonymous,
                userModel: userModel,
                user: user,
                authProvider: authProvider,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildResponsiveLayout({
    required BoxConstraints constraints,
    required bool isAnonymous,
    required dynamic userModel,
    required dynamic user,
    required AuthProvider authProvider,
  }) {
    final isTablet = constraints.maxWidth > 768;
    final isDesktop = constraints.maxWidth > 1200;

    if (isDesktop) {
      return _buildDesktopLayout(constraints, isAnonymous, userModel, user, authProvider);
    } else if (isTablet) {
      return _buildTabletLayout(constraints, isAnonymous, userModel, user, authProvider);
    } else {
      return _buildMobileLayout(constraints, isAnonymous, userModel, user, authProvider);
    }
  }

  // تخطيط الهاتف المحمول
  Widget _buildMobileLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return CustomScrollView(
      slivers: [
        _buildResponsiveAppBar(constraints, isAnonymous),
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.all(_getHorizontalPadding(constraints)),
            child: Column(
              children: _buildProfileContent(constraints, isAnonymous, userModel, user, authProvider),
            ),
          ),
        ),
      ],
    );
  }

  // تخطيط الجهاز اللوحي
  Widget _buildTabletLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return CustomScrollView(
      slivers: [
        _buildResponsiveAppBar(constraints, isAnonymous),
        SliverToBoxAdapter(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: _getHorizontalPadding(constraints),
              vertical: 24,
            ),
            child: _buildTabletGrid(constraints, isAnonymous, userModel, user, authProvider),
          ),
        ),
      ],
    );
  }

  // تخطيط سطح المكتب
  Widget _buildDesktopLayout(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return Row(
      children: [
        // الشريط الجانبي للمعلومات الأساسية
        Container(
          width: 350,
          color: Colors.white,
          child: Column(
            children: [
              _buildResponsiveAppBar(constraints, isAnonymous, isDesktop: true),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      _buildMainInfoCard(isAnonymous, userModel, user),
                      const SizedBox(height: 16),
                      if (!isAnonymous && userModel != null)
                        _buildActionButtons(context, userModel),
                      if (isAnonymous)
                        _buildGuestOptionsCard(context),
                      const Spacer(),
                      _buildSignOutButton(context, authProvider),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // المحتوى الرئيسي
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: _buildDesktopGrid(constraints, isAnonymous, userModel, user, authProvider),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // حساب المسافة الأفقية حسب حجم الشاشة
  double _getHorizontalPadding(BoxConstraints constraints) {
    if (constraints.maxWidth > 1200) {
      return constraints.maxWidth * 0.1; // 10% من عرض الشاشة للشاشات الكبيرة
    } else if (constraints.maxWidth > 768) {
      return 32.0; // مسافة ثابتة للأجهزة اللوحية
    } else {
      return 16.0; // مسافة صغيرة للهواتف
    }
  }

  // بناء شريط التطبيق المتجاوب
  Widget _buildResponsiveAppBar(BoxConstraints constraints, bool isAnonymous, {bool isDesktop = false}) {
    double expandedHeight = isDesktop ? 120 : (constraints.maxWidth > 768 ? 200 : 180);
    double avatarRadius = isDesktop ? 35 : (constraints.maxWidth > 768 ? 50 : 45);
    double iconSize = isDesktop ? 40 : (constraints.maxWidth > 768 ? 55 : 50);

    if (isDesktop) {
      return Container(
        height: expandedHeight,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade500,
              Colors.blue.shade700,
              Colors.blue.shade900,
            ],
            stops: const [0.0, 0.5, 1.0],
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: avatarRadius,
                backgroundColor: Colors.white,
                child: Icon(
                  isAnonymous ? Icons.person_outline : Icons.person,
                  size: iconSize,
                  color: Colors.blue.shade700,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return SliverAppBar(
      expandedHeight: expandedHeight,
      floating: false,
      pinned: true,
      elevation: 0,
      title: const Text(
        'الملف الشخصي',
        style: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      backgroundColor: Colors.blue.shade700,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue.shade500,
                Colors.blue.shade700,
                Colors.blue.shade900,
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.shade200.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                const SizedBox(height: 60),
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 3),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 15,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.1),
                        blurRadius: 5,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: avatarRadius,
                    backgroundColor: Colors.white,
                    child: Icon(
                      isAnonymous ? Icons.person_outline : Icons.person,
                      size: iconSize,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // بناء محتوى البروفايل للهاتف المحمول
  List<Widget> _buildProfileContent(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    List<Widget> widgets = [
      _buildMainInfoCard(isAnonymous, userModel, user),
      const SizedBox(height: 16),
    ];

    if (!isAnonymous && userModel != null) {
      widgets.addAll([
        _buildAcademicInfoCard(userModel),
        const SizedBox(height: 16),
        _buildPersonalInfoCard(userModel),
        const SizedBox(height: 16),
        _buildProjectsStatsCard(context),
        const SizedBox(height: 16),
        _buildActionButtons(context, userModel),
      ]);
    }

    if (isAnonymous) {
      widgets.add(_buildGuestOptionsCard(context));
    }

    widgets.addAll([
      const SizedBox(height: 16),
      _buildSignOutButton(context, authProvider),
      const SizedBox(height: 32),
    ]);

    return widgets;
  }

  // بناء شبكة للجهاز اللوحي
  Widget _buildTabletGrid(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    return Column(
      children: [
        // الصف الأول: المعلومات الأساسية والأكاديمية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildMainInfoCard(isAnonymous, userModel, user),
            ),
            const SizedBox(width: 16),
            if (!isAnonymous && userModel != null)
              Expanded(
                child: _buildAcademicInfoCard(userModel),
              ),
          ],
        ),

        if (!isAnonymous && userModel != null) ...[
          const SizedBox(height: 16),
          // الصف الثاني: المعلومات الشخصية والإحصائيات
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: _buildPersonalInfoCard(userModel),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildProjectsStatsCard(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildActionButtons(context, userModel),
        ],

        if (isAnonymous) ...[
          const SizedBox(height: 16),
          _buildGuestOptionsCard(context),
        ],

        const SizedBox(height: 16),
        _buildSignOutButton(context, authProvider),
      ],
    );
  }

  // بناء شبكة لسطح المكتب
  Widget _buildDesktopGrid(
    BoxConstraints constraints,
    bool isAnonymous,
    dynamic userModel,
    dynamic user,
    AuthProvider authProvider,
  ) {
    if (isAnonymous) {
      return Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          child: _buildGuestOptionsCard(context),
        ),
      );
    }

    if (userModel == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Column(
      children: [
        // الصف الأول: المعلومات الأكاديمية والشخصية
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: _buildAcademicInfoCard(userModel),
            ),
            const SizedBox(width: 24),
            Expanded(
              child: _buildPersonalInfoCard(userModel),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // الصف الثاني: إحصائيات المشاريع
        _buildProjectsStatsCard(context),
      ],
    );
  }

  // بطاقة المعلومات الأساسية
  Widget _buildMainInfoCard(bool isAnonymous, userModel, user) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Colors.blue.shade50,
            ],
          ),
        ),
        padding: const EdgeInsets.all(24.0),
        child: Column(
          children: [
            // اسم المستخدم
            Text(
              isAnonymous
                  ? 'مستخدم ضيف'
                  : (userModel?.fullName ?? user?.email ?? 'مستخدم'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),

            // البريد الإلكتروني
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.email, size: 16, color: Colors.blue.shade700),
                  const SizedBox(width: 8),
                  Text(
                    isAnonymous
                        ? 'تم تسجيل الدخول كضيف'
                        : (user?.email ?? 'مستخدم مسجل'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // حالة النشاط
            if (!isAnonymous && userModel != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: userModel.isActive ? Colors.green.shade50 : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: userModel.isActive ? Colors.green.shade200 : Colors.grey.shade200,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: userModel.isActive ? Colors.green : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      userModel.isActive
                          ? 'نشط الآن'
                          : 'غير نشط منذ ${userModel.inactiveTime}',
                      style: TextStyle(
                        fontSize: 14,
                        color: userModel.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // إحصائيات المتابعة
            if (!isAnonymous && userModel != null) ...[
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.purple.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.purple.shade200),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showFollowersList(context, userModel),
                        child: _buildFollowStatItem(
                          icon: Icons.people,
                          count: userModel.followersCount,
                          label: 'متابع',
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    Container(
                      width: 1,
                      height: 50,
                      color: Colors.grey.shade300,
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _showFollowingList(context, userModel),
                        child: _buildFollowStatItem(
                          icon: Icons.person_add,
                          count: userModel.followingCount,
                          label: 'متابَع',
                          color: Colors.green,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // النبذة الشخصية
            if (!isAnonymous && userModel != null && userModel.bio.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, size: 20, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        const Text(
                          'النبذة الشخصية',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      userModel.bio,
                      style: const TextStyle(fontSize: 14, height: 1.5),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الأكاديمية
  Widget _buildAcademicInfoCard(userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.school, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الأكاديمية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.school, 'الجامعة', userModel.university),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.book, 'التخصص', userModel.major),
          ],
        ),
      ),
    );
  }

  // بطاقة المعلومات الشخصية
  Widget _buildPersonalInfoCard(userModel) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: Colors.blue.shade700, size: 24),
                const SizedBox(width: 12),
                const Text(
                  'المعلومات الشخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.cake, 'العمر', '${userModel.age} سنة'),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.calendar_today,
              'تاريخ الميلاد',
              '${userModel.birthDate.day}/${userModel.birthDate.month}/${userModel.birthDate.year}'
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.person_add,
              'تاريخ إنشاء الحساب',
              '${userModel.createdAt.day}/${userModel.createdAt.month}/${userModel.createdAt.year}'
            ),
          ],
        ),
      ),
    );
  }

  // أزرار الإجراءات
  Widget _buildActionButtons(BuildContext context, userModel) {
    return Column(
      children: [
        // زر تعديل الملف الشخصي
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => EditProfileScreen(userModel: userModel),
                ),
              );
            },
            icon: const Icon(Icons.edit),
            label: const Text('تعديل الملف الشخصي'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),

        // زر الإحصائيات الإدارية (للمدراء فقط)
        if (userModel.isAdmin) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const AdminStatisticsScreen(),
                  ),
                );
              },
              icon: const Icon(Icons.analytics_rounded),
              label: const Text('إحصائيات التطبيق'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],
      ],
    );
  }

  // بطاقة خيارات الضيف
  Widget _buildGuestOptionsCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade50,
              Colors.orange.shade100,
            ],
          ),
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              size: 48,
              color: Colors.orange.shade700,
            ),
            const SizedBox(height: 16),
            const Text(
              'أنت تستخدم التطبيق كضيف',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'سجل حساب جديد أو ادخل بحسابك لحفظ بياناتك في السحابة والاستفادة من جميع الميزات',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _signOutAndGoToAuth(context, Provider.of<AuthProvider>(context, listen: false)),
                    icon: const Icon(Icons.person_add),
                    label: const Text('إنشاء حساب'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _signOutAndGoToAuth(context, Provider.of<AuthProvider>(context, listen: false)),
                    icon: const Icon(Icons.login),
                    label: const Text('تسجيل دخول'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // زر تسجيل الخروج
  Widget _buildSignOutButton(BuildContext context, AuthProvider authProvider) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: (authProvider.isLoading || authProvider.isLoggingOut)
            ? null
            : () => _signOutAndGoToAuth(context, authProvider),
        icon: (authProvider.isLoading || authProvider.isLoggingOut)
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
            )
          : const Icon(Icons.logout),
        label: Text(
          (authProvider.isLoading || authProvider.isLoggingOut)
              ? 'جاري تسجيل الخروج...'
              : 'تسجيل الخروج'
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 14),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ),
    );
  }

  // دالة مساعدة لعرض صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.blue.shade700),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  // عرض قائمة المتابعين
  void _showFollowersList(BuildContext context, dynamic userModel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // العنوان
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  children: [
                    Icon(Icons.people, color: Colors.blue.shade700, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المتابعون (${userModel.followersCount})',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(),

              // قائمة المتابعين
              Expanded(
                child: _buildFollowersListContent(scrollController, userModel),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عرض قائمة المتابَعين
  void _showFollowingList(BuildContext context, dynamic userModel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // مقبض السحب
              Container(
                margin: const EdgeInsets.symmetric(vertical: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // العنوان
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                child: Row(
                  children: [
                    Icon(Icons.person_add, color: Colors.green.shade700, size: 24),
                    const SizedBox(width: 12),
                    Text(
                      'المتابَعون (${userModel.followingCount})',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              const Divider(),

              // قائمة المتابَعين
              Expanded(
                child: _buildFollowingListContent(scrollController, userModel),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFollowStatItem({
    required IconData icon,
    required int count,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 28),
        const SizedBox(height: 8),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  // محتوى قائمة المتابعين
  Widget _buildFollowersListContent(ScrollController scrollController, dynamic userModel) {
    // في الوقت الحالي، سنعرض رسالة أن هذه الميزة قيد التطوير
    // يمكن تطويرها لاحقاً لجلب البيانات الفعلية من Firebase
    return ListView(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      children: [
        if (userModel.followersCount == 0) ...[
          const SizedBox(height: 50),
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.people_outline,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا يوجد متابعون بعد',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'شارك مخططاتك الذهنية لجذب المتابعين',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ] else ...[
          // عرض المتابعين (مؤقت - يمكن تطويره لاحقاً)
          for (int i = 0; i < userModel.followersCount && i < 10; i++)
            _buildFollowUserItem(
              name: 'متابع ${i + 1}',
              email: 'follower${i + 1}@example.com',
              isFollowing: false,
              onTap: () {
                // يمكن إضافة التنقل لملف المستخدم هنا
              },
            ),

          if (userModel.followersCount > 10) ...[
            const SizedBox(height: 20),
            Center(
              child: Text(
                'و ${userModel.followersCount - 10} متابع آخر...',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],

        const SizedBox(height: 20),
      ],
    );
  }

  // محتوى قائمة المتابَعين
  Widget _buildFollowingListContent(ScrollController scrollController, dynamic userModel) {
    return ListView(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      children: [
        if (userModel.followingCount == 0) ...[
          const SizedBox(height: 50),
          Center(
            child: Column(
              children: [
                Icon(
                  Icons.person_add_outlined,
                  size: 64,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'لا تتابع أحداً بعد',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'ابحث عن مستخدمين لمتابعتهم',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ] else ...[
          // عرض المتابَعين (مؤقت - يمكن تطويره لاحقاً)
          for (int i = 0; i < userModel.followingCount && i < 10; i++)
            _buildFollowUserItem(
              name: 'مستخدم ${i + 1}',
              email: 'user${i + 1}@example.com',
              isFollowing: true,
              onTap: () {
                // يمكن إضافة التنقل لملف المستخدم هنا
              },
            ),

          if (userModel.followingCount > 10) ...[
            const SizedBox(height: 20),
            Center(
              child: Text(
                'و ${userModel.followingCount - 10} مستخدم آخر...',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ],
        ],

        const SizedBox(height: 20),
      ],
    );
  }

  // عنصر مستخدم في قائمة المتابعة
  Widget _buildFollowUserItem({
    required String name,
    required String email,
    required bool isFollowing,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade100,
          child: Icon(
            Icons.person,
            color: Colors.blue.shade700,
          ),
        ),
        title: Text(
          name,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          email,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 14,
          ),
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: isFollowing ? Colors.green.shade50 : Colors.blue.shade50,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isFollowing ? Colors.green.shade200 : Colors.blue.shade200,
            ),
          ),
          child: Text(
            isFollowing ? 'متابَع' : 'متابع',
            style: TextStyle(
              color: isFollowing ? Colors.green.shade700 : Colors.blue.shade700,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  // بطاقة إحصائيات المشاريع
  Widget _buildProjectsStatsCard(BuildContext context) {
    return Consumer<MindMapProvider>(
      builder: (context, mindMapProvider, child) {
        final totalMindMaps = mindMapProvider.mindMaps.length;
        final totalSubjects = mindMapProvider.subjects.length;
        final favoriteMindMaps = mindMapProvider.mindMaps.where((m) => m.isFavorite).length;
        final recentMindMaps = mindMapProvider.mindMaps
            .where((m) => DateTime.now().difference(m.updatedAt).inDays <= 7)
            .length;

        return Card(
          elevation: 8,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.purple.shade50,
                  Colors.blue.shade50,
                ],
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        color: Colors.purple.shade700,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'إحصائيات المشاريع',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // شبكة الإحصائيات
                GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 1.8,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                  children: [
                    _buildStatItem(
                      icon: Icons.psychology_outlined,
                      title: 'الخرائط الذهنية',
                      value: totalMindMaps.toString(),
                      color: Colors.blue,
                    ),
                    _buildStatItem(
                      icon: Icons.school_outlined,
                      title: 'المواد الدراسية',
                      value: totalSubjects.toString(),
                      color: Colors.green,
                    ),
                    _buildStatItem(
                      icon: Icons.favorite_outline,
                      title: 'المفضلة',
                      value: favoriteMindMaps.toString(),
                      color: Colors.red,
                    ),
                    _buildStatItem(
                      icon: Icons.schedule_outlined,
                      title: 'الحديثة',
                      value: recentMindMaps.toString(),
                      color: Colors.orange,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // عنصر إحصائية واحد
  Widget _buildStatItem({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Flexible(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 11,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // دالة تسجيل الخروج والانتقال لشاشة المصادقة
  Future<void> _signOutAndGoToAuth(BuildContext context, AuthProvider authProvider) async {
    // إظهار حوار تأكيد
    final shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );

    if (shouldSignOut == true && context.mounted) {
      // عرض شاشة التحميل
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => LogoutLoadingScreen(
            title: 'جاري تسجيل الخروج...',
            subtitle: 'شكراً لاستخدام التطبيق',
            duration: const Duration(seconds: 3),
            gradientColors: [
              Colors.blue.shade500,
              Colors.blue.shade700,
              Colors.blue.shade900,
            ],
            onComplete: () async {
              // تسجيل الخروج الفعلي
              await authProvider.signOut();
              if (context.mounted) {
                Navigator.of(context).pushNamedAndRemoveUntil('/auth', (route) => false);
              }
            },
          ),
        ),
      );
    }
  }
}