import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';

import 'package:firebase_database/firebase_database.dart';
import '../models/mind_map.dart';
import '../models/mind_map_node.dart';
import '../models/subject.dart';

class MindMapProvider extends ChangeNotifier {
  final FirebaseDatabase _database = FirebaseDatabase.instance;
  List<MindMap> _mindMaps = [];
  List<Subject> _subjects = [];
  MindMap? _currentMindMap;
  Subject? _currentSubject;
  bool _isLoading = false;
  String? _error;
  String? _currentUserId;

  // Getters
  List<MindMap> get mindMaps => _mindMaps;
  List<Subject> get subjects => _subjects;
  MindMap? get currentMindMap => _currentMindMap;
  Subject? get currentSubject => _currentSubject;
  bool get isLoading => _isLoading;
  String? get error => _error;
  String? get currentUserId => _currentUserId;

  // تحويل البيانات من Firebase إلى Map<String, dynamic>
  Map<String, dynamic> _convertToStringDynamicMap(dynamic data) {
    if (data is Map<String, dynamic>) {
      return data;
    } else if (data is Map) {
      final result = <String, dynamic>{};
      for (var entry in data.entries) {
        final key = entry.key.toString();
        final value = entry.value;

        if (value is Map) {
          result[key] = _convertToStringDynamicMap(value);
        } else if (value is List) {
          result[key] = value.map((item) {
            if (item is Map) {
              return _convertToStringDynamicMap(item);
            }
            return item;
          }).toList();
        } else {
          result[key] = value;
        }
      }
      return result;
    } else {
      throw Exception('البيانات ليست من نوع Map: ${data.runtimeType}');
    }
  }

  // تعيين المستخدم الحالي
  Future<void> setCurrentUser(String? userId) async {
    print('🔄 تعيين المستخدم الحالي: $userId (السابق: $_currentUserId)');

    if (_currentUserId != userId) {
      final previousUserId = _currentUserId;
      _currentUserId = userId;

      print('🔄 تغيير المستخدم من $previousUserId إلى $userId');

      // إذا كان المستخدم ينتقل من ضيف إلى مسجل، نقل البيانات
      if (previousUserId == null && userId != null && (_mindMaps.isNotEmpty || _subjects.isNotEmpty)) {
        print('📦 ترحيل البيانات من التخزين المحلي إلى Firebase');
        await _migrateLocalDataToFirebase();
      }

      // مسح البيانات المحلية عند تغيير المستخدم
      print('🗑️ مسح البيانات المحلية');
      _mindMaps.clear();
      _subjects.clear();
      _currentMindMap = null;
      _currentSubject = null;

      // تحميل بيانات المستخدم الجديد
      if (userId != null) {
        print('📡 تحميل بيانات المستخدم الجديد');
        await loadData();
      } else {
        print('💾 تحميل بيانات الضيف');
        await loadData();
      }
      notifyListeners();
    } else {
      print('✅ نفس المستخدم، لا حاجة للتغيير');
    }
  }

  // ترحيل البيانات من التخزين المحلي إلى Firebase
  Future<void> _migrateLocalDataToFirebase() async {
    if (_currentUserId == null) return;

    try {
      print('🔄 ترحيل البيانات من التخزين المحلي إلى Firebase...');

      // حفظ البيانات الحالية في Firebase
      await _saveToFirebase();

      // مسح البيانات من التخزين المحلي
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('subjects');
      await prefs.remove('mindMaps');

      print('✅ تم ترحيل البيانات بنجاح');
    } catch (e) {
      print('❌ خطأ في ترحيل البيانات: $e');
    }
  }

  // تحديد حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // تحديد رسالة الخطأ
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // تحميل البيانات من Firebase أو التخزين المحلي
  Future<void> loadData() async {
    _setLoading(true);
    _setError(null);

    try {
      print('🔄 بدء تحميل البيانات...');
      print('👤 المستخدم الحالي: $_currentUserId');

      if (_currentUserId != null) {
        print('📡 تحميل من Firebase للمستخدم المسجل');
        // تحميل من Firebase للمستخدمين المسجلين
        await _loadFromFirebase();
      } else {
        print('💾 تحميل من التخزين المحلي للضيف');
        // تحميل من التخزين المحلي للضيوف
        await _loadFromLocal();
      }
    } catch (e) {
      print('❌ خطأ في تحميل البيانات: $e');
      _setError('خطأ في تحميل البيانات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // تحميل البيانات من Firebase
  Future<void> _loadFromFirebase() async {
    if (_currentUserId == null) return;

    try {
      print('🔄 تحميل بيانات المستخدم من Firebase: $_currentUserId');

      // تحميل المواد الدراسية
      final subjectsRef = _database.ref('userProjects/$_currentUserId/subjects');
      final subjectsSnapshot = await subjectsRef.once();

      print('📊 حالة المواد الدراسية: exists=${subjectsSnapshot.snapshot.exists}, value=${subjectsSnapshot.snapshot.value}');

      _subjects = [];
      if (subjectsSnapshot.snapshot.exists && subjectsSnapshot.snapshot.value != null) {
        try {
          final dynamic rawData = subjectsSnapshot.snapshot.value;
          print('📋 نوع البيانات الخام للمواد: ${rawData.runtimeType}');
          print('📋 البيانات الخام للمواد: $rawData');

          if (rawData is Map) {
            final subjectsData = Map<String, dynamic>.from(rawData);
            for (var entry in subjectsData.entries) {
              try {
                final subjectData = _convertToStringDynamicMap(entry.value);
                final subject = Subject.fromJson(subjectData);
                _subjects.add(subject);
              } catch (e) {
                print('❌ خطأ في تحويل مادة دراسية: $e');
              }
            }
            print('✅ تم تحميل ${_subjects.length} مادة دراسية');
          }
        } catch (e) {
          print('❌ خطأ في تحويل بيانات المواد الدراسية: $e');
          _subjects = [];
        }
      } else {
        print('⚠️ لا توجد مواد دراسية في Firebase');
      }

      // تحميل المخططات الذهنية
      final mindMapsRef = _database.ref('userProjects/$_currentUserId/mindMaps');
      final mindMapsSnapshot = await mindMapsRef.once();

      print('📊 حالة المخططات الذهنية: exists=${mindMapsSnapshot.snapshot.exists}, value=${mindMapsSnapshot.snapshot.value}');

      _mindMaps = [];
      if (mindMapsSnapshot.snapshot.exists && mindMapsSnapshot.snapshot.value != null) {
        try {
          final dynamic rawData = mindMapsSnapshot.snapshot.value;
          print('🗺️ نوع البيانات الخام للمخططات: ${rawData.runtimeType}');
          print('🗺️ البيانات الخام للمخططات: $rawData');

          if (rawData is Map) {
            final mindMapsData = Map<String, dynamic>.from(rawData);
            for (var entry in mindMapsData.entries) {
              try {
                final mindMapData = _convertToStringDynamicMap(entry.value);
                final mindMap = MindMap.fromJson(mindMapData);
                _mindMaps.add(mindMap);
              } catch (e) {
                print('❌ خطأ في تحويل مخطط ذهني: $e');
              }
            }
            print('✅ تم تحميل ${_mindMaps.length} مخطط ذهني');
          }
        } catch (e) {
          print('❌ خطأ في تحويل بيانات المخططات الذهنية: $e');
          _mindMaps = [];
        }
      } else {
        print('⚠️ لا توجد مخططات ذهنية في Firebase');
      }

      // إشعار المستمعين بالتحديث
      notifyListeners();
    } catch (e) {
      print('❌ خطأ في تحميل البيانات من Firebase: $e');
      rethrow;
    }
  }

  // تحميل البيانات من التخزين المحلي (للضيوف)
  Future<void> _loadFromLocal() async {
    try {
      print('🔄 تحميل البيانات من التخزين المحلي');

      final prefs = await SharedPreferences.getInstance();

      // تحميل المواد الدراسية
      final subjectsJson = prefs.getString('subjects');
      if (subjectsJson != null) {
        final List<dynamic> subjectsList = jsonDecode(subjectsJson);
        _subjects = subjectsList.map((json) => Subject.fromJson(json)).toList();
      }

      // تحميل المخططات الذهنية
      final mindMapsJson = prefs.getString('mindMaps');
      if (mindMapsJson != null) {
        final List<dynamic> mindMapsList = jsonDecode(mindMapsJson);
        _mindMaps = mindMapsList.map((json) => MindMap.fromJson(json)).toList();
      }

      print('✅ تم تحميل ${_subjects.length} مادة و ${_mindMaps.length} مخطط من التخزين المحلي');
    } catch (e) {
      print('❌ خطأ في تحميل البيانات من التخزين المحلي: $e');
      rethrow;
    }
  }

  // حفظ البيانات في Firebase أو التخزين المحلي
  Future<void> saveData() async {
    try {
      if (_currentUserId != null) {
        // حفظ في Firebase للمستخدمين المسجلين
        await _saveToFirebase();
      } else {
        // حفظ في التخزين المحلي للضيوف
        await _saveToLocal();
      }
    } catch (e) {
      _setError('خطأ في حفظ البيانات: $e');
    }
  }

  // حفظ البيانات في Firebase
  Future<void> _saveToFirebase() async {
    if (_currentUserId == null) return;

    try {
      print('💾 حفظ البيانات في Firebase للمستخدم: $_currentUserId');
      print('📊 عدد المواد الدراسية: ${_subjects.length}');
      print('📊 عدد المخططات الذهنية: ${_mindMaps.length}');

      // حفظ المواد الدراسية
      final subjectsRef = _database.ref('userProjects/$_currentUserId/subjects');
      final subjectsData = <String, dynamic>{};
      for (var subject in _subjects) {
        subjectsData[subject.id] = subject.toJson();
        print('📋 حفظ مادة: ${subject.name} (${subject.id})');
      }
      await subjectsRef.set(subjectsData);
      print('✅ تم حفظ ${_subjects.length} مادة دراسية');

      // حفظ المخططات الذهنية
      final mindMapsRef = _database.ref('userProjects/$_currentUserId/mindMaps');
      final mindMapsData = <String, dynamic>{};
      for (var mindMap in _mindMaps) {
        mindMapsData[mindMap.id] = mindMap.toJson();
        print('🗺️ حفظ مخطط: ${mindMap.title} (${mindMap.id})');
      }
      await mindMapsRef.set(mindMapsData);
      print('✅ تم حفظ ${_mindMaps.length} مخطط ذهني');

      print('✅ تم حفظ البيانات في Firebase بنجاح');
    } catch (e) {
      print('❌ خطأ في حفظ البيانات في Firebase: $e');
      rethrow;
    }
  }

  // حفظ البيانات في التخزين المحلي (للضيوف)
  Future<void> _saveToLocal() async {
    try {
      print('💾 حفظ البيانات في التخزين المحلي');

      final prefs = await SharedPreferences.getInstance();

      // حفظ المواد الدراسية
      final subjectsJson = jsonEncode(_subjects.map((s) => s.toJson()).toList());
      await prefs.setString('subjects', subjectsJson);

      // حفظ المخططات الذهنية
      final mindMapsJson = jsonEncode(_mindMaps.map((m) => m.toJson()).toList());
      await prefs.setString('mindMaps', mindMapsJson);

      print('✅ تم حفظ البيانات في التخزين المحلي بنجاح');
    } catch (e) {
      print('❌ خطأ في حفظ البيانات في التخزين المحلي: $e');
      rethrow;
    }
  }

  // إضافة مادة دراسية جديدة
  Future<void> addSubject(Subject subject) async {
    _subjects.add(subject);
    await saveData();
    notifyListeners();
  }

  // تحديث مادة دراسية
  Future<void> updateSubject(Subject updatedSubject) async {
    final index = _subjects.indexWhere((s) => s.id == updatedSubject.id);
    if (index != -1) {
      _subjects[index] = updatedSubject;
      await saveData();
      notifyListeners();
    }
  }

  // حذف مادة دراسية
  Future<void> deleteSubject(String subjectId) async {
    // حذف جميع المخططات الذهنية المرتبطة بهذه المادة
    _mindMaps.removeWhere((mindMap) => mindMap.subject == subjectId);
    
    // حذف المادة
    _subjects.removeWhere((subject) => subject.id == subjectId);
    
    await saveData();
    notifyListeners();
  }

  // إضافة مخطط ذهني جديد
  Future<void> addMindMap(MindMap mindMap) async {
    _mindMaps.add(mindMap);
    
    // إضافة المخطط إلى المادة المرتبطة به
    if (mindMap.subject.isNotEmpty) {
      final subject = _subjects.firstWhere(
        (s) => s.name == mindMap.subject,
        orElse: () => Subject(name: mindMap.subject),
      );
      
      if (!_subjects.contains(subject)) {
        _subjects.add(subject);
      }
      
      subject.addMindMap(mindMap.id);
    }
    
    await saveData();
    notifyListeners();
  }

  // تحديث مخطط ذهني
  Future<void> updateMindMap(MindMap updatedMindMap) async {
    final index = _mindMaps.indexWhere((m) => m.id == updatedMindMap.id);
    if (index != -1) {
      _mindMaps[index] = updatedMindMap;
      await saveData();
      notifyListeners();
    }
  }

  // حذف مخطط ذهني
  Future<void> deleteMindMap(String mindMapId) async {
    final mindMap = _mindMaps.firstWhere((m) => m.id == mindMapId);
    
    // إزالة المخطط من المادة المرتبطة به
    if (mindMap.subject.isNotEmpty) {
      final subject = _subjects.firstWhere(
        (s) => s.name == mindMap.subject,
        orElse: () => Subject(name: ''),
      );
      subject.removeMindMap(mindMapId);
    }
    
    _mindMaps.removeWhere((m) => m.id == mindMapId);
    await saveData();
    notifyListeners();
  }

  // تحديد المخطط الذهني الحالي
  void setCurrentMindMap(MindMap? mindMap) {
    _currentMindMap = mindMap;
    notifyListeners();
  }

  // تحديد المادة الدراسية الحالية
  void setCurrentSubject(Subject? subject) {
    _currentSubject = subject;
    notifyListeners();
  }

  // الحصول على المخططات الذهنية لمادة معينة
  List<MindMap> getMindMapsForSubject(String subjectName) {
    return _mindMaps.where((mindMap) => mindMap.subject == subjectName).toList();
  }

  // البحث في المخططات الذهنية
  List<MindMap> searchMindMaps(String query) {
    if (query.isEmpty) return _mindMaps;

    return _mindMaps.where((mindMap) {
      return mindMap.title.toLowerCase().contains(query.toLowerCase()) ||
             mindMap.description.toLowerCase().contains(query.toLowerCase()) ||
             mindMap.subject.toLowerCase().contains(query.toLowerCase()) ||
             mindMap.tags.any((tag) => tag.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  // تحميل مخطط ذهني محدد من Firebase
  Future<MindMap?> loadMindMapById(String mindMapId) async {
    try {
      print('🔄 تحميل المخطط الذهني من Firebase: $mindMapId');

      // البحث في جميع المستخدمين عن المخطط
      final usersRef = _database.ref('userProjects');
      final usersSnapshot = await usersRef.once();

      if (usersSnapshot.snapshot.exists) {
        final usersData = usersSnapshot.snapshot.value as Map<dynamic, dynamic>;

        for (final userId in usersData.keys) {
          final userMindMapsRef = _database.ref('userProjects/$userId/mindMaps/$mindMapId');
          final mindMapSnapshot = await userMindMapsRef.once();

          if (mindMapSnapshot.snapshot.exists) {
            final mindMapData = mindMapSnapshot.snapshot.value as Map<dynamic, dynamic>;
            final convertedData = _convertToStringDynamicMap(mindMapData);
            final mindMap = MindMap.fromJson(convertedData);

            // إضافة المخطط للقائمة المحلية مؤقتاً
            _mindMaps.add(mindMap);
            notifyListeners();

            print('✅ تم تحميل المخطط الذهني: ${mindMap.title}');
            return mindMap;
          }
        }
      }

      print('❌ لم يتم العثور على المخطط الذهني: $mindMapId');
      return null;
    } catch (e) {
      print('❌ خطأ في تحميل المخطط الذهني: $e');
      return null;
    }
  }

  // الحصول على المخططات المفضلة
  List<MindMap> get favoriteMindMaps {
    return _mindMaps.where((mindMap) => mindMap.isFavorite).toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  // تبديل حالة المفضلة لمخطط ذهني
  Future<void> toggleMindMapFavorite(String mindMapId) async {
    final index = _mindMaps.indexWhere((m) => m.id == mindMapId);
    if (index != -1) {
      _mindMaps[index].toggleFavorite();
      await saveData();
      notifyListeners();
    }
  }

  // تعيين حالة المفضلة لمخطط ذهني
  Future<void> setMindMapFavorite(String mindMapId, bool isFavorite) async {
    final index = _mindMaps.indexWhere((m) => m.id == mindMapId);
    if (index != -1) {
      _mindMaps[index].setFavorite(isFavorite);
      await saveData();
      notifyListeners();
    }
  }

  // البحث في المواد الدراسية
  List<Subject> searchSubjects(String query) {
    if (query.isEmpty) return _subjects;

    return _subjects.where((subject) {
      return subject.name.toLowerCase().contains(query.toLowerCase()) ||
             subject.description.toLowerCase().contains(query.toLowerCase()) ||
             (subject.professor?.toLowerCase().contains(query.toLowerCase()) ?? false);
    }).toList();
  }



  // تصدير مخطط ذهني إلى ملف
  Future<String?> exportMindMap(MindMap mindMap) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${mindMap.title}.json');
      await file.writeAsString(mindMap.toJsonString());
      return file.path;
    } catch (e) {
      _setError('خطأ في تصدير المخطط: $e');
      return null;
    }
  }

  // استيراد مخطط ذهني من ملف
  Future<bool> importMindMap(String filePath) async {
    try {
      final file = File(filePath);
      final content = await file.readAsString();
      final mindMap = MindMap.fromJsonString(content);
      await addMindMap(mindMap);
      return true;
    } catch (e) {
      _setError('خطأ في استيراد المخطط: $e');
      return false;
    }
  }

  // مسح رسالة الخطأ
  void clearError() {
    _setError(null);
  }

  // إنشاء مخطط ذهني جديد مع عقدة جذر
  MindMap createNewMindMap({
    required String title,
    String description = '',
    String subject = '',
  }) {
    final mindMap = MindMap(
      title: title,
      description: description,
      subject: subject,
    );

    // إنشاء عقدة جذر
    final rootNode = MindMapNode(
      title: title,
      position: const Offset(400, 300), // موقع وسط الشاشة تقريباً
    );

    mindMap.addNode(rootNode);
    return mindMap;
  }
}
